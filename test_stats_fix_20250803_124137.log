2025-08-03 12:41:37,951 - TestStatsFix - INFO - 🚀 开始统计数据修复效果测试...
2025-08-03 12:41:37,952 - TestStatsFix - INFO - 🧪 测试GUI统计数据累加修复...
2025-08-03 12:41:42,787 - TestStatsFix - INFO - 第一次更新后: {'processed_contacts': 5, 'successful_adds': 3, 'failed_adds': 2, 'total_contacts': 100}
2025-08-03 12:41:42,787 - TestStatsFix - INFO - 第二次更新后: {'processed_contacts': 10, 'successful_adds': 6, 'failed_adds': 4, 'total_contacts': 100}
2025-08-03 12:41:42,788 - TestStatsFix - INFO - ✅ GUI统计数据累加修复测试通过
2025-08-03 12:41:42,790 - TestStatsFix - INFO - 🧪 测试状态分类修复...
2025-08-03 12:41:42,791 - TestStatsFix - INFO - ✅ 状态映射测试通过: already_friend -> 失败
2025-08-03 12:41:42,791 - TestStatsFix - INFO - ✅ 状态映射测试通过: success -> 成功
2025-08-03 12:41:42,792 - TestStatsFix - INFO - ✅ 状态映射测试通过: user_not_found -> 失败
2025-08-03 12:41:42,792 - TestStatsFix - INFO - ✅ 状态映射测试通过: add_to_contacts -> 成功
2025-08-03 12:41:42,792 - TestStatsFix - INFO - 🧪 测试控制器统计数据更新...
2025-08-03 12:41:42,794 - main_controller - INFO - 🚀 开始初始化主控制器...
2025-08-03 12:41:42,795 - main_controller - INFO - 📋 正在初始化配置管理器...
2025-08-03 12:41:42,812 - main_controller - INFO - ✅ 配置管理器初始化完成
2025-08-03 12:41:42,812 - main_controller - INFO - 📊 正在初始化数据管理器...
2025-08-03 12:41:42,830 - main_controller - INFO - ✅ 数据管理器初始化完成
2025-08-03 12:41:42,835 - main_controller - INFO - ⏱️ 正在初始化频率错误处理器...
2025-08-03 12:41:42,843 - main_controller - INFO - ✅ 频率错误处理器初始化完成
2025-08-03 12:41:42,843 - main_controller - INFO - 🪟 正在初始化窗口管理器...
2025-08-03 12:41:42,846 - modules.window_manager - INFO - ✅ 配置文件加载成功: config.json
2025-08-03 12:41:42,848 - main_controller - INFO - ✅ 窗口管理器初始化完成
2025-08-03 12:41:42,849 - main_controller - INFO - 🖥️ 正在初始化主界面管理器...
2025-08-03 12:41:42,850 - modules.main_interface - INFO - ✅ 配置文件加载成功: config.json
2025-08-03 12:41:42,851 - modules.main_interface - INFO - ✅ 微信主界面操作模块初始化完成
2025-08-03 12:41:42,851 - main_controller - INFO - ✅ 主界面管理器初始化完成
2025-08-03 12:41:42,852 - main_controller - INFO - 👥 正在初始化自动添加好友模块...
2025-08-03 12:41:42,854 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-03 12:41:42,856 - main_controller - INFO - ✅ 自动添加好友模块初始化完成
2025-08-03 12:41:42,856 - main_controller - INFO - 📝 正在初始化好友申请处理器...
2025-08-03 12:41:42,858 - modules.friend_request_window - INFO - ✅ 已加载配置文件: config.json
2025-08-03 12:41:42,859 - modules.friend_request_window - INFO - 🔧 使用固定坐标配置:
2025-08-03 12:41:42,863 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-03 12:41:42,864 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-03 12:41:42,865 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-03 12:41:42,865 - modules.friend_request_window - INFO - ✅ 微信好友申请窗口处理器初始化完成
2025-08-03 12:41:42,866 - main_controller - INFO - ✅ 好友申请处理器初始化完成
2025-08-03 12:41:42,866 - main_controller - INFO - 🔗 正在建立组件间引用关系...
2025-08-03 12:41:42,866 - modules.window_manager - INFO - ✅ 已设置频率错误处理器引用
2025-08-03 12:41:42,873 - main_controller - INFO - ✅ 组件间引用关系建立完成
2025-08-03 12:41:42,874 - main_controller - INFO - 🧠 正在启用智能检测功能...
2025-08-03 12:41:42,875 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-03 12:41:42,875 - main_controller - INFO - ✅ 智能检测功能已启用
2025-08-03 12:41:42,876 - main_controller - INFO - 🔍 支持的检测方法:
2025-08-03 12:41:42,877 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-08-03 12:41:42,882 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-08-03 12:41:42,883 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-08-03 12:41:42,883 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-03 12:41:42,884 - main_controller - INFO - 🖱️ 支持的点击方法:
2025-08-03 12:41:42,884 - main_controller - INFO -    1. 使用Win32 API点击
2025-08-03 12:41:42,885 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-08-03 12:41:42,886 - main_controller - INFO -    3. 使用键盘Enter键
2025-08-03 12:41:42,886 - main_controller - INFO -    4. 发送窗口消息
2025-08-03 12:41:42,887 - main_controller - INFO - 🔄 重试设置: 检测最多3次, 点击最多4次
2025-08-03 12:41:42,888 - main_controller - INFO - 📱 已启用跨分辨率兼容性
2025-08-03 12:41:42,889 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-03 12:41:42,891 - main_controller - INFO - ✅ 智能检测功能启用完成
2025-08-03 12:41:42,893 - main_controller - INFO - ⚙️ 正在初始化执行状态...
2025-08-03 12:41:42,895 - main_controller - INFO - ✅ 执行状态初始化完成
2025-08-03 12:41:42,900 - main_controller - INFO - 📞 正在初始化GUI回调函数...
2025-08-03 12:41:42,901 - main_controller - INFO - ✅ GUI回调函数初始化完成
2025-08-03 12:41:42,901 - main_controller - INFO - 📊 正在初始化执行统计...
2025-08-03 12:41:42,902 - main_controller - INFO - ✅ 执行统计初始化完成
2025-08-03 12:41:42,902 - main_controller - INFO - 🎉 主控制器初始化完全完成！
2025-08-03 12:41:42,902 - main_controller - INFO - ✅ 微信自动化主控制器初始化完成
2025-08-03 12:41:42,902 - main_controller - INFO - 📅 当前北京时间: 2025-08-03 20:41:42
2025-08-03 12:41:42,903 - main_controller - INFO - ⏰ 时间段配置:
2025-08-03 12:41:42,903 - main_controller - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-03 12:41:42,903 - main_controller - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-03 12:41:42,904 - main_controller - INFO - 🕐 正在检查时间权限 - 当前北京时间: 20:41
2025-08-03 12:41:42,904 - main_controller - INFO - 📋 时间段配置检查:
2025-08-03 12:41:42,905 - main_controller - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-03 12:41:42,905 - main_controller - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-03 12:41:42,906 - main_controller - INFO - ⏰ 当前时间 20:41 不在上午时段 08:00-12:00 内
2025-08-03 12:41:42,906 - main_controller - INFO - ✅ 时间验证通过 - 当前时间 20:41 在下午时段 14:00-23:59 内
2025-08-03 12:41:42,907 - main_controller - INFO - ✅ 当前时间在允许的执行时间段内
2025-08-03 12:41:42,907 - TestStatsFix - INFO - ✅ 控制器统计数据更新测试通过
2025-08-03 12:41:42,908 - TestStatsFix - INFO - 
============================================================
2025-08-03 12:41:42,908 - TestStatsFix - INFO - 📊 统计数据修复测试结果汇总
2025-08-03 12:41:42,908 - TestStatsFix - INFO - ============================================================
2025-08-03 12:41:42,909 - TestStatsFix - INFO - GUI统计数据累加修复: ✅ 通过
2025-08-03 12:41:42,909 - TestStatsFix - INFO - 状态分类修复: ✅ 通过
2025-08-03 12:41:42,909 - TestStatsFix - INFO - 控制器统计数据更新: ✅ 通过
2025-08-03 12:41:42,910 - TestStatsFix - INFO - 
总计: 3/3 项测试通过
2025-08-03 12:41:42,910 - TestStatsFix - INFO - 🎉 所有测试通过，统计数据修复成功！
