#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试表格显示功能
验证联系人列表中的"结果"和"最后更新时间"列是否正确显示
"""

import tkinter as tk
from tkinter import ttk
import os
from datetime import datetime

def test_table_display():
    """测试表格显示功能"""
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("表格显示测试")
    root.geometry("800x600")
    
    # 创建表格
    columns = ("序号", "手机号码", "状态", "结果", "最后更新时间")
    tree = ttk.Treeview(root, columns=columns, show='headings', height=15)
    
    # 配置表格列（使用优化后的设置）
    column_widths = {
        "序号": 60, 
        "手机号码": 120, 
        "状态": 80, 
        "结果": 150, 
        "最后更新时间": 160
    }
    
    for col in columns:
        tree.heading(col, text=col)
        tree.column(
            col, 
            width=column_widths[col], 
            minwidth=column_widths[col] - 20,
            anchor=tk.CENTER,
            stretch=True
        )
    
    # 设置样式
    style = ttk.Style()
    style.configure("Treeview", 
                   font=('Arial', 9), 
                   rowheight=28,
                   fieldbackground='white')
    style.configure("Treeview.Heading",
                   font=('Microsoft YaHei UI', 12, 'bold'),
                   background='#3498db',
                   foreground='#ffffff')
    
    # 添加测试数据
    test_data = [
        (1, "17717556675", "成功", "添加成功", "2025-01-28 14:30:25"),
        (2, "19813817002", "成功", "已是好友", "2025-01-28 14:31:10"),
        (3, "13971347880", "失败", "用户不存在", "2025-01-28 14:32:05"),
        (4, "17368236495", "成功", "添加成功", "2025-01-28 14:33:15"),
        (5, "16677007826", "待处理", "未处理", "未更新"),
        (6, "18186537072", "成功", "好友请求已发送", "2025-01-28 14:35:20"),
        (7, "18153496461", "失败", "操作过于频繁", "2025-01-28 14:36:30"),
        (8, "15090789059", "成功", "添加成功", "2025-01-28 14:37:45"),
    ]
    
    for data in test_data:
        tree.insert('', 'end', values=data)
    
    # 自动调整列宽功能
    def on_configure(event):
        _ = event
        try:
            total_width = tree.winfo_width()
            if total_width > 100:
                col_ratios = {"序号": 0.08, "手机号码": 0.22, "状态": 0.12, "结果": 0.28, "最后更新时间": 0.30}
                for col, ratio in col_ratios.items():
                    new_width = int(total_width * ratio)
                    tree.column(col, width=new_width)
        except Exception:
            pass
    
    tree.bind('<Configure>', on_configure)
    
    # 添加滚动条
    scrollbar_y = ttk.Scrollbar(root, orient=tk.VERTICAL, command=tree.yview)
    scrollbar_x = ttk.Scrollbar(root, orient=tk.HORIZONTAL, command=tree.xview)
    tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
    
    # 布局
    tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
    scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
    
    # 添加说明标签
    info_label = tk.Label(root, 
                         text="测试表格显示 - 检查'结果'和'最后更新时间'列是否完整显示",
                         font=('Arial', 10),
                         bg='lightblue',
                         pady=5)
    info_label.pack(side=tk.TOP, fill=tk.X)
    
    print("✅ 表格显示测试窗口已启动")
    print("📋 测试数据包含8行记录，请检查'结果'和'最后更新时间'列是否完整显示")
    print("🔧 窗口支持列宽自动调整和手动调整")
    
    root.mainloop()

if __name__ == "__main__":
    test_table_display()
