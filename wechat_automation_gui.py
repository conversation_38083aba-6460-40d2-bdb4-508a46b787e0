#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微信自动化添加好友 - 图形用户界面
功能：为main_controller.py提供可视化操作界面

核心特性：
1. 实时监控执行状态和进度
2. 可视化配置管理
3. 日志实时显示
4. 统计信息展示
5. 一键启动/停止控制
6. 多窗口状态监控

版本：1.0.0
作者：AI助手
创建时间：2025-01-28
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import threading
import queue
import json
import time
import os
from datetime import datetime, timezone, timedelta
from pathlib import Path
import logging
from typing import Optional

# 导入主控制器
from main_controller import WeChatMainController


class WeChatAutomationGUI:
    """微信自动化添加好友图形用户界面"""
    
    def __init__(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.root.title("微信自动化添加好友控制台 v1.0.0")
        self.root.geometry("1200x800")
        self.root.minsize(1100, 650)  # 增加最小尺寸确保按钮完整显示

        # 设置现代化背景色
        self.root.configure(bg='#f8f9fa')

        # 🔧 GUI性能优化设置
        self.root.option_add('*tearOff', False)  # 禁用菜单撕离
        self.root.resizable(True, True)  # 允许调整大小但优化重绘

        # 设置图标和样式
        self.setup_styles()
        
        # 控制器和状态
        self.controller: Optional[WeChatMainController] = None
        self.is_running = False
        self.is_paused = False
        self.stop_requested = False
        self.automation_thread: Optional[threading.Thread] = None
        
        # 消息队列用于线程间通信
        self.message_queue = queue.Queue()

        # 🔧 GUI更新控制机制 - 修复闪屏和卡顿问题
        self.last_update_time = 0
        self.update_lock = threading.Lock()
        self.pending_updates = {
            "statistics": False,
            "status": False,
            "progress": False
        }
        self.update_throttle_interval = 2.0  # 最小更新间隔2000ms，彻底消除闪烁

        # 状态数据
        self.execution_stats = {
            "total_contacts": 0,
            "processed_contacts": 0,
            "successful_adds": 0,
            "failed_adds": 0,
            "skipped_contacts": 0,
            "total_windows": 0,
            "completed_windows": 0,
            "start_time": None,
            "end_time": None
        }

        # 🔧 上次显示的数据缓存 - 用于检测实际变化
        self.last_displayed_stats = self.execution_stats.copy()

        # 🔧 日志去重机制 - 防止重复日志显示
        self.recent_logs = []  # 存储最近的日志消息
        self.max_recent_logs = 10  # 最多保存10条最近日志用于去重
        self.last_step_message = ""  # 上次执行步骤消息，避免重复

        # 运行时参数变量
        self.runtime_params = {
            "interval_min": tk.StringVar(value="50"),
            "interval_max": tk.StringVar(value="60"),
            "daily_limit": tk.StringVar(value="200"),
            "max_per_window": tk.StringVar(value="20"),
            "morning_start": tk.StringVar(value="08:00"),
            "morning_end": tk.StringVar(value="12:00"),
            "afternoon_start": tk.StringVar(value="14:00"),
            "afternoon_end": tk.StringVar(value="23:59"),
            "rest_trigger": tk.StringVar(value="20"),
            "rest_duration": tk.StringVar(value="5")
        }

        # 时段启用/禁用状态变量
        self.time_slot_enabled = {
            "morning_enabled": tk.BooleanVar(value=True),
            "afternoon_enabled": tk.BooleanVar(value=True)
        }

        # 运行状态变量
        self.status_vars = {
            "total_progress": tk.StringVar(value="0/0 (0%)"),
            "planned_count": tk.StringVar(value="0"),
            "current_progress": tk.StringVar(value="0"),
            "success_count": tk.StringVar(value="0"),
            "error_count": tk.StringVar(value="0"),
            "countdown": tk.StringVar(value="0")
        }
        
        # 创建界面
        self.create_widgets()
        self.setup_logging()
        
        # 启动消息处理
        self.process_messages()

        # 🔧 启动状态显示更新循环
        self.update_status_display()

        # 加载配置
        self.load_configuration()
        self.load_runtime_params_from_config()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')

        # 现代化配色方案
        colors = {
            'primary': '#3498db',      # 主色调 - 蓝色
            'secondary': '#2ecc71',    # 次要色 - 绿色
            'accent': '#e74c3c',       # 强调色 - 红色
            'warning': '#f39c12',      # 警告色 - 橙色
            'dark': '#2c3e50',         # 深色文字
            'light': '#ecf0f1',        # 浅色背景
            'white': '#ffffff',        # 白色
            'gray': '#95a5a6',         # 灰色
            'success': '#27ae60',      # 成功色
            'info': '#3498db'          # 信息色
        }

        # 优化字体配置 - 使用更现代的字体
        fonts = {
            'title': ('Microsoft YaHei UI', 14, 'bold'),
            'large': ('Microsoft YaHei UI', 12),
            'medium': ('Microsoft YaHei UI', 11),
            'small': ('Microsoft YaHei UI', 10),
            'button': ('Microsoft YaHei UI', 11, 'bold'),
            'mono': ('Consolas', 11)
        }

        # 标签样式
        style.configure('Title.TLabel', font=fonts['title'], foreground=colors['dark'])
        style.configure('Status.TLabel', font=fonts['large'], foreground=colors['dark'])
        style.configure('Success.TLabel', foreground=colors['success'], font=fonts['large'])
        style.configure('Error.TLabel', foreground=colors['accent'], font=fonts['large'])
        style.configure('Warning.TLabel', foreground=colors['warning'], font=fonts['large'])
        style.configure('Large.TLabel', font=fonts['large'], foreground=colors['dark'])
        style.configure('Medium.TLabel', font=fonts['medium'], foreground=colors['dark'])

        # 现代化按钮样式
        style.configure('Large.TButton',
                       font=fonts['button'],
                       padding=(15, 10),
                       relief='flat',
                       borderwidth=0,
                       background=colors['primary'],
                       foreground=colors['white'])

        style.configure('Medium.TButton',
                       font=fonts['medium'],
                       padding=(12, 8),
                       relief='flat',
                       borderwidth=0,
                       background=colors['secondary'],
                       foreground=colors['white'])

        style.configure('Small.TButton',
                       font=fonts['small'],
                       padding=(8, 6),
                       relief='flat',
                       borderwidth=0,
                       background=colors['gray'],
                       foreground=colors['white'])

        # 紧凑按钮样式 - 专为执行控制按钮设计
        style.configure('Compact.TButton',
                       font=fonts['medium'],
                       padding=(8, 6),  # 减少padding使按钮更紧凑
                       relief='flat',
                       borderwidth=0,
                       background=colors['secondary'],
                       foreground=colors['white'])

        # 现代化输入框样式
        style.configure('Large.TEntry',
                       font=fonts['large'],
                       fieldbackground=colors['white'],
                       padding=(10, 8),
                       borderwidth=1,
                       relief='solid')

        style.configure('Medium.TEntry',
                       font=fonts['medium'],
                       fieldbackground=colors['white'],
                       padding=(8, 6),
                       borderwidth=1,
                       relief='solid')

        # 复选框样式
        style.configure('Medium.TCheckbutton',
                       font=fonts['medium'],
                       foreground=colors['dark'],
                       background=colors['light'])

        # 增强的悬停和点击效果
        style.map('Large.TButton',
                 background=[('active', '#2980b9'), ('pressed', '#21618c')],
                 foreground=[('active', colors['white']), ('pressed', colors['white'])],
                 relief=[('pressed', 'flat'), ('!pressed', 'flat')],
                 borderwidth=[('active', 2), ('!active', 0)])

        style.map('Medium.TButton',
                 background=[('active', '#27ae60'), ('pressed', '#229954')],
                 foreground=[('active', colors['white']), ('pressed', colors['white'])],
                 relief=[('pressed', 'flat'), ('!pressed', 'flat')],
                 borderwidth=[('active', 2), ('!active', 0)])

        style.map('Small.TButton',
                 background=[('active', '#7f8c8d'), ('pressed', '#6c7b7d')],
                 foreground=[('active', colors['white']), ('pressed', colors['white'])],
                 relief=[('pressed', 'flat'), ('!pressed', 'flat')],
                 borderwidth=[('active', 2), ('!active', 0)])

        # 紧凑按钮悬停效果
        style.map('Compact.TButton',
                 background=[('active', '#27ae60'), ('pressed', '#229954')],
                 foreground=[('active', colors['white']), ('pressed', colors['white'])],
                 relief=[('pressed', 'flat'), ('!pressed', 'flat')],
                 borderwidth=[('active', 1), ('!active', 0)])

        # 增强的输入框悬停效果
        style.map('Large.TEntry',
                 fieldbackground=[('focus', '#ebf3fd'), ('!focus', colors['white'])],
                 bordercolor=[('focus', colors['primary']), ('!focus', colors['gray'])],
                 borderwidth=[('focus', 2), ('!focus', 1)])

        style.map('Medium.TEntry',
                 fieldbackground=[('focus', '#ebf3fd'), ('!focus', colors['white'])],
                 bordercolor=[('focus', colors['primary']), ('!focus', colors['gray'])],
                 borderwidth=[('focus', 2), ('!focus', 1)])

        # 增强的复选框悬停效果
        style.map('Medium.TCheckbutton',
                 background=[('active', "#ad1093"), ('!active', colors['light'])],
                 foreground=[('active', colors['primary']), ('!active', colors['dark'])])

        # 下拉菜单悬停效果
        style.configure('TCombobox',
                       font=fonts['medium'],
                       fieldbackground=colors['white'],
                       borderwidth=1,
                       relief='solid')

        style.map('TCombobox',
                 fieldbackground=[('focus', '#ebf3fd'), ('!focus', colors['white'])],
                 bordercolor=[('focus', colors['primary']), ('!focus', colors['gray'])],
                 borderwidth=[('focus', 2), ('!focus', 1)],
                 selectbackground=[('focus', colors['primary'])])

        # 标签页悬停效果增强
        style.map('TNotebook.Tab',
                 background=[('selected', colors['white']), ('active', '#e8f4fd'), ('!selected', colors['light'])],
                 foreground=[('selected', colors['primary']), ('active', colors['primary']), ('!selected', colors['dark'])],
                 borderwidth=[('selected', 2), ('active', 1), ('!selected', 0)])

        # 框架样式
        style.configure('TLabelframe',
                       background=colors['white'],
                       borderwidth=1,
                       relief='solid')

        style.configure('TLabelframe.Label',
                       font=fonts['medium'],
                       foreground=colors['dark'],
                       background=colors['white'])

        # 笔记本样式
        style.configure('TNotebook',
                       background=colors['light'],
                       borderwidth=0)

        style.configure('TNotebook.Tab',
                       font=fonts['medium'],
                       padding=(15, 8),
                       background=colors['light'],
                       foreground=colors['dark'])

        style.map('TNotebook.Tab',
                 background=[('selected', colors['white']), ('!selected', colors['light'])],
                 foreground=[('selected', colors['primary']), ('!selected', colors['dark'])])

        # 🔧 增强进度条样式 - 高对比度设计
        # 首先复制默认布局
        style.layout('Enhanced.TProgressbar', [
            ('Progressbar.trough', {
                'children': [('Progressbar.pbar', {'side': 'left', 'sticky': 'ns'})],
                'sticky': 'nswe'
            })
        ])

        # 主进度条样式 - 亮绿色前景，深灰色背景
        style.configure('Enhanced.TProgressbar',
                       background='#00FF00',        # 亮绿色前景 (已完成部分)
                       troughcolor='#404040',       # 深灰色背景 (未完成部分)
                       borderwidth=1,               # 边框宽度
                       relief='solid',              # 实线边框
                       lightcolor='#00FF00',        # 亮绿色高光
                       darkcolor='#00CC00')         # 深绿色阴影

        # 蓝色主题进度条样式 - 亮蓝色前景，浅灰色背景
        style.layout('Blue.TProgressbar', [
            ('Progressbar.trough', {
                'children': [('Progressbar.pbar', {'side': 'left', 'sticky': 'ns'})],
                'sticky': 'nswe'
            })
        ])
        style.configure('Blue.TProgressbar',
                       background='#0080FF',        # 亮蓝色前景
                       troughcolor='#E0E0E0',       # 浅灰色背景
                       borderwidth=1,
                       relief='solid',
                       lightcolor='#0080FF',
                       darkcolor='#0066CC')

        # 成功主题进度条样式 - 翠绿色
        style.layout('Success.TProgressbar', [
            ('Progressbar.trough', {
                'children': [('Progressbar.pbar', {'side': 'left', 'sticky': 'ns'})],
                'sticky': 'nswe'
            })
        ])
        style.configure('Success.TProgressbar',
                       background='#27AE60',        # 翠绿色前景
                       troughcolor='#404040',       # 深灰色背景
                       borderwidth=1,
                       relief='solid',
                       lightcolor='#2ECC71',
                       darkcolor='#229954')

        # 保留原有样式作为备用
        style.configure('TProgressbar',
                       background=colors['primary'],
                       troughcolor=colors['light'],
                       borderwidth=0,
                       lightcolor=colors['primary'],
                       darkcolor=colors['primary'])

        # 🔧 添加进度条动画效果配置
        # 动画进度条样式 - 带脉冲效果
        style.configure('Animated.TProgressbar',
                       background='#00FF00',
                       troughcolor='#404040',
                       borderwidth=2,
                       relief='solid',
                       bordercolor='#2c3e50',
                       lightcolor='#00FF00',
                       darkcolor='#00CC00',
                       thickness=25)

        # 光标样式设置已简化

    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        self.create_main_frame()
        
        # 创建菜单栏
        self.create_menu()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建主要内容区域
        self.create_content_area()
        
        # 创建状态栏
        self.create_status_bar()
        
    def create_main_frame(self):
        """创建主框架"""
        # 主容器 - 使用现代化背景
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 设置主框架背景色
        style = ttk.Style()
        style.configure('Main.TFrame', background='#f8f9fa')
        
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开配置文件", command=self.open_config_file)
        file_menu.add_command(label="打开Excel文件", command=self.open_excel_file)
        file_menu.add_separator()
        file_menu.add_command(label="导出日志", command=self.export_logs)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="配置验证", command=self.validate_configuration)
        tools_menu.add_command(label="清理日志", command=self.clean_logs)
        tools_menu.add_command(label="重置统计", command=self.reset_statistics)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
        
    def create_toolbar(self):
        """创建工具栏"""
        # 创建现代化工具栏容器
        toolbar_container = ttk.Frame(self.main_frame)
        toolbar_container.pack(fill=tk.X, pady=(0, 15))

        # 工具栏背景框架
        toolbar = ttk.Frame(toolbar_container)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        # 左侧按钮组
        left_buttons = ttk.Frame(toolbar)
        left_buttons.pack(side=tk.LEFT, fill=tk.Y)

        # 主要控制按钮 - 使用现代化图标和样式
        self.start_button = ttk.Button(
            left_buttons, text="🚀 开始自动化",
            command=self.start_automation,
            style='Large.TButton'
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 8))

        self.stop_button = ttk.Button(
            left_buttons, text="⏹ 停止",
            command=self.stop_automation,
            state=tk.DISABLED,
            style='Large.TButton'
        )
        self.stop_button.pack(side=tk.LEFT, padx=(0, 8))

        self.pause_button = ttk.Button(
            left_buttons, text="⏸ 暂停",
            command=self.pause_automation,
            state=tk.DISABLED,
            style='Large.TButton'
        )
        self.pause_button.pack(side=tk.LEFT, padx=(0, 15))

        # 分隔线
        separator = ttk.Separator(left_buttons, orient=tk.VERTICAL)
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=10)

        # 功能按钮
        stats_button = ttk.Button(
            left_buttons, text="📊 统计报告",
            command=self.show_statistics_dialog,
            style='Medium.TButton'
        )
        stats_button.pack(side=tk.LEFT, padx=(10, 8))

        # 右侧状态区域
        status_frame = ttk.Frame(toolbar)
        status_frame.pack(side=tk.RIGHT, fill=tk.Y)

        # 北京时间显示 - 显眼位置，准确时区
        time_container = ttk.Frame(status_frame)
        time_container.pack(side=tk.RIGHT, padx=(0, 20))

        # 时间图标
        time_icon = ttk.Label(time_container, text="🕐", font=('Microsoft YaHei UI', 16))
        time_icon.pack(side=tk.LEFT, padx=(0, 8))

        # 时间显示区域
        time_display_frame = ttk.Frame(time_container)
        time_display_frame.pack(side=tk.LEFT)

        # 时间标签 - 使用更大更醒目的字体
        self.main_time_label = ttk.Label(
            time_display_frame, text="",
            font=('Microsoft YaHei UI', 14, 'bold'),
            foreground='#2c3e50'
        )
        self.main_time_label.pack(anchor=tk.W)

        # 状态指示器 - 使用更现代的样式
        self.status_indicator = ttk.Label(
            status_frame, text="🟢 系统已就绪",
            style='Success.TLabel',
            font=('Microsoft YaHei UI', 12, 'bold')
        )
        self.status_indicator.pack(side=tk.RIGHT, padx=(10, 0))
        
    def create_content_area(self):
        """创建主要内容区域"""
        # 创建现代化笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.main_frame, style='TNotebook')
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建各个标签页
        self.create_control_tab()
        self.create_progress_tab()
        self.create_logs_tab()
        self.create_config_tab()
        
    def create_control_tab(self):
        """创建控制标签页"""
        # 创建带背景的控制面板
        control_frame = ttk.Frame(self.notebook)
        self.notebook.add(control_frame, text="🎮 控制面板")

        # 设置控制面板背景
        control_frame.configure(style='Main.TFrame')

        # 创建主要的两列布局 - 精细调整左右间距平衡
        left_column = ttk.Frame(control_frame)
        left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(6, 10))

        right_column = ttk.Frame(control_frame)
        right_column.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 22))

        # 左列：执行控制 + 运行参数配置
        self.create_execution_control_section(left_column)
        self.create_runtime_params_section(left_column)

        # 右列：实时状态 + 运行状态监控
        self.create_current_status_section(right_column)
        self.create_status_monitor_section(right_column)

    def create_execution_control_section(self, parent):
        """创建执行控制区域"""
        # 现代化执行控制框架 - 优化间距，减少垂直空间占用
        control_frame = ttk.LabelFrame(parent, text="📋 执行控制", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 8))

        # 文件选择区域 - 减少垂直间距
        file_frame = ttk.Frame(control_frame)
        file_frame.pack(fill=tk.X, pady=(0, 12))

        # 文件选择标题
        file_title = ttk.Label(file_frame, text="📁 Excel文件选择", style='Large.TLabel')
        file_title.pack(anchor=tk.W, pady=(0, 8))

        excel_frame = ttk.Frame(file_frame)
        excel_frame.pack(fill=tk.X)

        self.excel_path_var = tk.StringVar(value="添加好友名单.xlsx")
        self.excel_entry = ttk.Entry(excel_frame, textvariable=self.excel_path_var, style='Large.TEntry')
        self.excel_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # 现代化浏览按钮
        browse_btn = ttk.Button(
            excel_frame, text="📂 浏览文件",
            command=self.browse_excel_file,
            style='Medium.TButton'
        )
        browse_btn.pack(side=tk.RIGHT)

        # 移除基础参数区域 - 根据用户要求删除

    def create_current_status_section(self, parent):
        """创建当前状态区域"""
        # 现代化状态显示框架
        status_frame = ttk.LabelFrame(parent, text="📊 实时状态", padding=20)
        status_frame.pack(fill=tk.X, pady=(0, 20))

        # 状态卡片容器
        status_cards = ttk.Frame(status_frame)
        status_cards.pack(fill=tk.X)

        # 当前状态卡片
        status_card = ttk.Frame(status_cards)
        status_card.pack(fill=tk.X, pady=(0, 12))

        status_icon = ttk.Label(status_card, text="🔄", font=('Microsoft YaHei UI', 16))
        status_icon.pack(side=tk.LEFT, padx=(0, 10))

        self.current_status_label = ttk.Label(
            status_card, text="当前状态: 初始化已完成",
            style='Title.TLabel'
        )
        self.current_status_label.pack(side=tk.LEFT, anchor=tk.W)

        # 执行步骤卡片
        step_card = ttk.Frame(status_cards)
        step_card.pack(fill=tk.X, pady=(0, 12))

        step_icon = ttk.Label(step_card, text="⚡", font=('Microsoft YaHei UI', 16))
        step_icon.pack(side=tk.LEFT, padx=(0, 10))

        self.current_step_label = ttk.Label(
            step_card, text="执行步骤: 待启动",
            style='Large.TLabel'
        )
        self.current_step_label.pack(side=tk.LEFT, anchor=tk.W)

        # 微信窗口检测卡片
        window_card = ttk.Frame(status_cards)
        window_card.pack(fill=tk.X)

        window_icon = ttk.Label(window_card, text="🖥️", font=('Microsoft YaHei UI', 16))
        window_icon.pack(side=tk.LEFT, padx=(0, 10))

        self.window_detection_label = ttk.Label(
            window_card, text="微信窗口: 未检测",
            style='Large.TLabel'
        )
        self.window_detection_label.pack(side=tk.LEFT, anchor=tk.W)

    def create_runtime_params_section(self, parent):
        """创建运行参数配置区域"""
        params_frame = ttk.LabelFrame(parent, text="⚙️ 运行参数配置", padding=8)
        params_frame.pack(fill=tk.X, expand=False, pady=(0, 0))  # 改为不扩展，确保按钮可见



        # 使用网格布局替代滚动框架，充分利用水平空间
        main_grid = ttk.Frame(params_frame)
        main_grid.pack(fill=tk.BOTH, expand=True)

        # 配置网格列权重，创建两列布局
        main_grid.grid_columnconfigure(0, weight=1)
        main_grid.grid_columnconfigure(1, weight=1)

        # 配置网格行权重
        main_grid.grid_rowconfigure(0, weight=0)  # 间隔和限制配置行
        main_grid.grid_rowconfigure(1, weight=0)  # 时段配置行
        main_grid.grid_rowconfigure(2, weight=0)  # 休息配置行

        # 左列：单次添加间隔配置 - 减少padding和间距
        interval_frame = ttk.LabelFrame(main_grid, text="⏱️ 单次添加间隔", padding=8)
        interval_frame.grid(row=0, column=0, sticky="ew", padx=(0, 8), pady=(0, 8))

        # 配置间隔框架的网格
        interval_frame.grid_columnconfigure(1, weight=1)

        # 最小值
        ttk.Label(interval_frame, text="最小值(秒):", style='Medium.TLabel').grid(row=0, column=0, sticky=tk.W, pady=2)
        min_control_frame = ttk.Frame(interval_frame)
        min_control_frame.grid(row=0, column=1, sticky=tk.E, pady=2)

        ttk.Button(min_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["interval_min"], -1, 1, 300)).pack(side=tk.LEFT)
        interval_min_entry = ttk.Entry(min_control_frame, textvariable=self.runtime_params["interval_min"], width=6, style='Medium.TEntry')
        interval_min_entry.pack(side=tk.LEFT, padx=2)
        interval_min_entry.bind('<KeyRelease>', self.on_param_change)
        ttk.Button(min_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["interval_min"], 1, 1, 300)).pack(side=tk.LEFT)

        # 最大值
        ttk.Label(interval_frame, text="最大值(秒):", style='Medium.TLabel').grid(row=1, column=0, sticky=tk.W, pady=2)
        max_control_frame = ttk.Frame(interval_frame)
        max_control_frame.grid(row=1, column=1, sticky=tk.E, pady=2)

        ttk.Button(max_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["interval_max"], -1, 1, 300)).pack(side=tk.LEFT)
        interval_max_entry = ttk.Entry(max_control_frame, textvariable=self.runtime_params["interval_max"], width=6, style='Medium.TEntry')
        interval_max_entry.pack(side=tk.LEFT, padx=2)
        interval_max_entry.bind('<KeyRelease>', self.on_param_change)
        ttk.Button(max_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["interval_max"], 1, 1, 300)).pack(side=tk.LEFT)

        # 右列：数量限制配置 - 减少padding和间距
        limits_frame = ttk.LabelFrame(main_grid, text="📊 数量限制", padding=8)
        limits_frame.grid(row=0, column=1, sticky="ew", padx=(8, 0), pady=(0, 8))

        # 配置限制框架的网格
        limits_frame.grid_columnconfigure(1, weight=1)

        # 每日添加上限
        ttk.Label(limits_frame, text="每日添加上限:（人数）", style='Medium.TLabel').grid(row=0, column=0, sticky=tk.W, pady=2)
        daily_control_frame = ttk.Frame(limits_frame)
        daily_control_frame.grid(row=0, column=1, sticky=tk.E, pady=2)

        ttk.Button(daily_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["daily_limit"], -10, 10, 1000)).pack(side=tk.LEFT)
        daily_entry = ttk.Entry(daily_control_frame, textvariable=self.runtime_params["daily_limit"], width=6, style='Medium.TEntry')
        daily_entry.pack(side=tk.LEFT, padx=2)
        daily_entry.bind('<KeyRelease>', self.on_param_change)
        ttk.Button(daily_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["daily_limit"], 10, 10, 1000)).pack(side=tk.LEFT)

        # 每窗口最大添加数量
        ttk.Label(limits_frame, text="单窗口最大限制:（人数）", style='Medium.TLabel').grid(row=1, column=0, sticky=tk.W, pady=2)
        window_control_frame = ttk.Frame(limits_frame)
        window_control_frame.grid(row=1, column=1, sticky=tk.E, pady=2)

        ttk.Button(window_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["max_per_window"], -1, 0, 100)).pack(side=tk.LEFT)
        window_entry = ttk.Entry(window_control_frame, textvariable=self.runtime_params["max_per_window"], width=6, style='Medium.TEntry')
        window_entry.pack(side=tk.LEFT, padx=2)
        window_entry.bind('<KeyRelease>', self.on_param_change)
        ttk.Button(window_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["max_per_window"], 1, 0, 100)).pack(side=tk.LEFT)

        # 左列：上午时段配置 - 减少padding和间距
        morning_frame = ttk.LabelFrame(main_grid, text="🌅 上午时段", padding=8)
        morning_frame.grid(row=1, column=0, sticky="ew", padx=(0, 8), pady=(8, 8))

        # 配置上午时段框架的网格
        morning_frame.grid_columnconfigure(1, weight=1)

        # 启用/禁用复选框
        morning_enabled_cb = ttk.Checkbutton(morning_frame, text="启用上午时段",
                                           variable=self.time_slot_enabled["morning_enabled"],
                                           command=self.on_morning_enabled_change,
                                           style='Medium.TCheckbutton')
        morning_enabled_cb.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 8))

        # 开始时间
        ttk.Label(morning_frame, text="开始时间:", style='Medium.TLabel').grid(row=1, column=0, sticky=tk.W, pady=2)
        morning_start_control_frame = ttk.Frame(morning_frame)
        morning_start_control_frame.grid(row=1, column=1, sticky=tk.E, pady=2)

        self.morning_start_minus_btn = ttk.Button(morning_start_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["morning_start"], -15))
        self.morning_start_minus_btn.pack(side=tk.LEFT)
        self.morning_start_entry = ttk.Entry(morning_start_control_frame, textvariable=self.runtime_params["morning_start"], width=6, style='Medium.TEntry')
        self.morning_start_entry.pack(side=tk.LEFT, padx=2)
        self.morning_start_entry.bind('<KeyRelease>', self.on_param_change)
        self.morning_start_plus_btn = ttk.Button(morning_start_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["morning_start"], 15))
        self.morning_start_plus_btn.pack(side=tk.LEFT)

        # 结束时间
        ttk.Label(morning_frame, text="结束时间:", style='Medium.TLabel').grid(row=2, column=0, sticky=tk.W, pady=2)
        morning_end_control_frame = ttk.Frame(morning_frame)
        morning_end_control_frame.grid(row=2, column=1, sticky=tk.E, pady=2)

        self.morning_end_minus_btn = ttk.Button(morning_end_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["morning_end"], -15))
        self.morning_end_minus_btn.pack(side=tk.LEFT)
        self.morning_end_entry = ttk.Entry(morning_end_control_frame, textvariable=self.runtime_params["morning_end"], width=6, style='Medium.TEntry')
        self.morning_end_entry.pack(side=tk.LEFT, padx=2)
        self.morning_end_entry.bind('<KeyRelease>', self.on_param_change)
        self.morning_end_plus_btn = ttk.Button(morning_end_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["morning_end"], 15))
        self.morning_end_plus_btn.pack(side=tk.LEFT)

        # 右列：下午时段配置
        afternoon_frame = ttk.LabelFrame(main_grid, text="🌇 下午时段", padding=8)
        afternoon_frame.grid(row=1, column=1, sticky="ew", padx=(8, 0), pady=(8, 8))

        # 配置下午时段框架的网格
        afternoon_frame.grid_columnconfigure(1, weight=1)

        # 启用/禁用复选框
        afternoon_enabled_cb = ttk.Checkbutton(afternoon_frame, text="启用下午时段",
                                             variable=self.time_slot_enabled["afternoon_enabled"],
                                             command=self.on_afternoon_enabled_change,
                                             style='Medium.TCheckbutton')
        afternoon_enabled_cb.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 8))

        # 开始时间
        ttk.Label(afternoon_frame, text="开始时间:", style='Medium.TLabel').grid(row=1, column=0, sticky=tk.W, pady=2)
        afternoon_start_control_frame = ttk.Frame(afternoon_frame)
        afternoon_start_control_frame.grid(row=1, column=1, sticky=tk.E, pady=2)

        self.afternoon_start_minus_btn = ttk.Button(afternoon_start_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["afternoon_start"], -15))
        self.afternoon_start_minus_btn.pack(side=tk.LEFT)
        self.afternoon_start_entry = ttk.Entry(afternoon_start_control_frame, textvariable=self.runtime_params["afternoon_start"], width=6, style='Medium.TEntry')
        self.afternoon_start_entry.pack(side=tk.LEFT, padx=2)
        self.afternoon_start_entry.bind('<KeyRelease>', self.on_param_change)
        self.afternoon_start_plus_btn = ttk.Button(afternoon_start_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["afternoon_start"], 15))
        self.afternoon_start_plus_btn.pack(side=tk.LEFT)

        # 结束时间
        ttk.Label(afternoon_frame, text="结束时间:", style='Medium.TLabel').grid(row=2, column=0, sticky=tk.W, pady=2)
        afternoon_end_control_frame = ttk.Frame(afternoon_frame)
        afternoon_end_control_frame.grid(row=2, column=1, sticky=tk.E, pady=2)

        self.afternoon_end_minus_btn = ttk.Button(afternoon_end_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["afternoon_end"], -15))
        self.afternoon_end_minus_btn.pack(side=tk.LEFT)
        self.afternoon_end_entry = ttk.Entry(afternoon_end_control_frame, textvariable=self.runtime_params["afternoon_end"], width=6, style='Medium.TEntry')
        self.afternoon_end_entry.pack(side=tk.LEFT, padx=2)
        self.afternoon_end_entry.bind('<KeyRelease>', self.on_param_change)
        self.afternoon_end_plus_btn = ttk.Button(afternoon_end_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_time_value(self.runtime_params["afternoon_end"], 15))
        self.afternoon_end_plus_btn.pack(side=tk.LEFT)

        # 左列：休息触发条件配置 - 优化布局，减少间距
        trigger_frame = ttk.LabelFrame(main_grid, text="⏸️ 休息触发条件", padding=8)
        trigger_frame.grid(row=2, column=0, sticky="nsew", padx=(0, 8), pady=(8, 0))

        # 确保触发条件框架的网格配置正确
        trigger_frame.grid_columnconfigure(0, weight=0)  # 标签列不扩展
        trigger_frame.grid_columnconfigure(1, weight=1)  # 控件列扩展
        trigger_frame.grid_rowconfigure(0, weight=0)
        trigger_frame.grid_rowconfigure(1, weight=0)

        # 触发数量设置
        ttk.Label(trigger_frame, text="每添加:", style='Medium.TLabel').grid(
            row=0, column=0, sticky="w", pady=(0, 8), padx=(0, 10))

        trigger_control_frame = ttk.Frame(trigger_frame)
        trigger_control_frame.grid(row=0, column=1, sticky="ew", pady=(0, 8))

        # 触发条件控制按钮
        self.rest_trigger_minus_btn = ttk.Button(trigger_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["rest_trigger"], -1, 1, 100))
        self.rest_trigger_minus_btn.pack(side=tk.LEFT)

        self.rest_trigger_entry = ttk.Entry(trigger_control_frame, textvariable=self.runtime_params["rest_trigger"],
                                          width=8, style='Medium.TEntry')
        self.rest_trigger_entry.pack(side=tk.LEFT, padx=3)
        self.rest_trigger_entry.bind('<KeyRelease>', self.on_param_change)

        self.rest_trigger_plus_btn = ttk.Button(trigger_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["rest_trigger"], 1, 1, 100))
        self.rest_trigger_plus_btn.pack(side=tk.LEFT)

        # 说明文字
        ttk.Label(trigger_frame, text="个好友后自动休息", style='Medium.TLabel').grid(
            row=1, column=0, columnspan=2, sticky="w", pady=(0, 0))

        # 右列：休息时长设置配置 - 优化布局，减少间距
        duration_frame = ttk.LabelFrame(main_grid, text="⏰ 休息时长设置", padding=8)
        duration_frame.grid(row=2, column=1, sticky="nsew", padx=(8, 0), pady=(8, 0))

        # 确保休息时长框架的网格配置正确
        duration_frame.grid_columnconfigure(0, weight=0)  # 标签列不扩展
        duration_frame.grid_columnconfigure(1, weight=1)  # 控件列扩展
        duration_frame.grid_rowconfigure(0, weight=0)
        duration_frame.grid_rowconfigure(1, weight=0)

        # 休息时长设置
        ttk.Label(duration_frame, text="休息:", style='Medium.TLabel').grid(
            row=0, column=0, sticky="w", pady=(0, 8), padx=(0, 5))

        duration_control_frame = ttk.Frame(duration_frame)
        duration_control_frame.grid(row=0, column=1, sticky="ew", pady=(0, 8))

        # 休息时长控制按钮
        self.rest_duration_minus_btn = ttk.Button(duration_control_frame, text="-", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["rest_duration"], -1, 1, 60))
        self.rest_duration_minus_btn.pack(side=tk.LEFT)

        self.rest_duration_entry = ttk.Entry(duration_control_frame, textvariable=self.runtime_params["rest_duration"],
                                           width=8, style='Medium.TEntry')
        self.rest_duration_entry.pack(side=tk.LEFT, padx=3)
        self.rest_duration_entry.bind('<KeyRelease>', self.on_param_change)

        self.rest_duration_plus_btn = ttk.Button(duration_control_frame, text="+", width=3, style='Small.TButton',
                  command=lambda: self.adjust_numeric_value(self.runtime_params["rest_duration"], 1, 1, 60))
        self.rest_duration_plus_btn.pack(side=tk.LEFT)

        # 说明文字
        ttk.Label(duration_frame, text="分钟后继续执行", style='Medium.TLabel').grid(
            row=1, column=0, columnspan=2, sticky="w", pady=(0, 0))

        # 创建底部按钮区域 - 位于所有参数设置之后
        bottom_button_frame = ttk.Frame(params_frame)
        bottom_button_frame.pack(fill=tk.X, pady=(15, 8))

        # 创建按钮容器，使用网格布局实现响应式设计
        button_grid = ttk.Frame(bottom_button_frame)
        button_grid.pack(expand=True)

        # 配置按钮网格列权重，确保三个按钮平均分配空间
        button_grid.grid_columnconfigure(0, weight=1)
        button_grid.grid_columnconfigure(1, weight=1)
        button_grid.grid_columnconfigure(2, weight=1)

        # 参数操作按钮 - 移动到底部，水平排列
        validate_btn = ttk.Button(button_grid, text="✅ 验证", command=self.validate_runtime_params,
                                width=8, style='Compact.TButton')
        validate_btn.grid(row=0, column=0, padx=10, pady=5, sticky="ew")

        save_btn = ttk.Button(button_grid, text="💾 保存", command=self.save_runtime_params,
                            width=8, style='Compact.TButton')
        save_btn.grid(row=0, column=1, padx=10, pady=5, sticky="ew")

        reset_btn = ttk.Button(button_grid, text="🔄 重置", command=self.reset_runtime_params,
                             width=8, style='Compact.TButton')
        reset_btn.grid(row=0, column=2, padx=10, pady=5, sticky="ew")

        # 初始化时段启用状态
        self.on_morning_enabled_change()
        self.on_afternoon_enabled_change()

    def create_status_monitor_section(self, parent):
        """创建运行状态监控区域"""
        # 现代化状态监控框架
        status_frame = ttk.LabelFrame(parent, text="📊 运行状态监控", padding=20)
        status_frame.pack(fill=tk.BOTH, expand=True)

        # 总体进度显示卡片
        progress_frame = ttk.LabelFrame(status_frame, text="📈 总体进度", padding=15)
        progress_frame.pack(fill=tk.X, pady=(0, 20))

        # 进度信息头部
        progress_header = ttk.Frame(progress_frame)
        progress_header.pack(fill=tk.X, pady=(0, 12))

        progress_icon = ttk.Label(progress_header, text="🎯", font=('Microsoft YaHei UI', 16))
        progress_icon.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(progress_header, text="执行进度:", style='Large.TLabel').pack(side=tk.LEFT)
        self.total_progress_label = ttk.Label(progress_header, textvariable=self.status_vars["total_progress"],
                                            font=('Microsoft YaHei UI', 12, 'bold'), foreground='#3498db')
        self.total_progress_label.pack(side=tk.RIGHT)

        # 🔧 增强现代化进度条 - 高对比度设计
        progress_container = ttk.Frame(progress_frame)
        progress_container.pack(fill=tk.X, pady=(5, 0))

        self.main_progress_bar = ttk.Progressbar(
            progress_container,
            mode='determinate',
            length=350,
            maximum=100  # 🔧 明确设置最大值为100
        )
        self.main_progress_bar.pack(fill=tk.X, pady=(0, 5), ipady=8)

        # 添加百分比显示标签
        self.main_progress_percent = ttk.Label(
            progress_container,
            text="0%",
            font=('Microsoft YaHei UI', 11, 'bold'),
            foreground='#00FF00'  # 与进度条前景色匹配
        )
        self.main_progress_percent.pack(anchor=tk.E)

        # 详细统计信息卡片
        stats_frame = ttk.LabelFrame(status_frame, text="📋 详细统计", padding=15)
        stats_frame.pack(fill=tk.BOTH, expand=True)

        # 统计网格容器
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.BOTH, expand=True)

        # 配置网格权重
        stats_grid.grid_columnconfigure(0, weight=1)
        stats_grid.grid_columnconfigure(1, weight=1)

        # 左列统计卡片
        left_stats = ttk.Frame(stats_grid)
        left_stats.grid(row=0, column=0, sticky="nsew", padx=(0, 10))

        # 右列统计卡片
        right_stats = ttk.Frame(stats_grid)
        right_stats.grid(row=0, column=1, sticky="nsew", padx=(10, 0))

        # 创建统计项目
        self.create_status_item(left_stats, "📋 计划处理:", self.status_vars["planned_count"])
        self.create_status_item(left_stats, "⚡ 当前进度:", self.status_vars["current_progress"])
        self.create_status_item(left_stats, "✅ 成功添加:", self.status_vars["success_count"], '#27ae60')

        # 🔧 隐藏失败/错误统计显示（根据用户要求）
        # self.create_status_item(right_stats, "❌ 失败/错误:", self.status_vars["error_count"], '#e74c3c')
        self.create_status_item(right_stats, "⏰ 操作倒计时:", self.status_vars["countdown"], '#f39c12')

        # 实时状态更新
        self.update_status_display()

    def create_status_item(self, parent, label_text, text_var, color='#2c3e50'):
        """创建现代化状态显示项"""
        # 状态项容器
        item_frame = ttk.Frame(parent)
        item_frame.pack(fill=tk.X, pady=8)

        # 状态项背景卡片
        card_frame = ttk.Frame(item_frame)
        card_frame.pack(fill=tk.X, padx=5, pady=2)

        # 标签和数值
        ttk.Label(card_frame, text=label_text,
                 font=('Microsoft YaHei UI', 11),
                 foreground='#34495e').pack(side=tk.LEFT, anchor=tk.W)

        status_label = ttk.Label(card_frame, textvariable=text_var,
                               font=('Microsoft YaHei UI', 11, 'bold'))
        status_label.configure(foreground=color)
        status_label.pack(side=tk.RIGHT, anchor=tk.E)

    def create_progress_tab(self):
        """创建数据状态标签页 - 重构版：基于Excel数据显示"""
        progress_frame = ttk.Frame(self.notebook)
        self.notebook.add(progress_frame, text="📊 数据状态")

        # 设置数据状态页面背景
        progress_frame.configure(style='Main.TFrame')

        # 🔧 新增：运行状态指示器区域
        status_frame = ttk.LabelFrame(progress_frame, text="🔄 运行状态", padding=15)
        status_frame.pack(fill=tk.X, padx=15, pady=15)

        # 状态指示器容器
        status_container = ttk.Frame(status_frame)
        status_container.pack(fill=tk.X)

        # 运行状态标签
        self.running_status_label = ttk.Label(
            status_container,
            text="⏸️ 系统待机",
            font=('Microsoft YaHei UI', 14, 'bold'),
            foreground='#7f8c8d'
        )
        self.running_status_label.pack(side=tk.LEFT)

        # 窗口检测状态
        self.window_status_label = ttk.Label(
            status_container,
            text="🔍 窗口检测：待检测",
            font=('Microsoft YaHei UI', 12),
            foreground='#95a5a6'
        )
        self.window_status_label.pack(side=tk.RIGHT)

        # 🔧 新增：Excel数据显示区域
        excel_frame = ttk.LabelFrame(progress_frame, text="📊 联系人列表（来自Excel文件）", padding=15)
        excel_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # 工具栏
        toolbar_frame = ttk.Frame(excel_frame)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # 刷新按钮
        self.refresh_excel_btn = ttk.Button(
            toolbar_frame,
            text="🔄 刷新数据",
            command=self.refresh_excel_data,
            style='Accent.TButton'
        )
        self.refresh_excel_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 数据统计标签
        self.excel_stats_label = ttk.Label(
            toolbar_frame,
            text="总计: 0 | 成功: 0 | 失败: 0 | 待处理: 0",
            font=('Microsoft YaHei UI', 11),
            foreground='#34495e'
        )
        self.excel_stats_label.pack(side=tk.RIGHT)

        # 联系人数据表格容器
        table_container = ttk.Frame(excel_frame)
        table_container.pack(fill=tk.BOTH, expand=True)

        # 创建联系人数据表格
        columns = ("序号", "手机号码", "状态", "结果", "最后更新时间")
        self.excel_data_tree = ttk.Treeview(table_container, columns=columns, show='headings', height=15)

        # 配置表格列（优化列宽以确保数据完整显示）
        column_widths = {
            "序号": 60,
            "手机号码": 120,
            "状态": 80,
            "结果": 150,
            "最后更新时间": 160
        }

        for col in columns:
            self.excel_data_tree.heading(col, text=col)
            # 设置列宽，允许用户调整，并确保最小宽度
            self.excel_data_tree.column(
                col,
                width=column_widths[col],
                minwidth=column_widths[col] - 20,
                anchor=tk.CENTER,
                stretch=True  # 允许列宽自动调整
            )

        # 添加滚动条
        excel_scrollbar_y = ttk.Scrollbar(table_container, orient=tk.VERTICAL, command=self.excel_data_tree.yview)
        excel_scrollbar_x = ttk.Scrollbar(table_container, orient=tk.HORIZONTAL, command=self.excel_data_tree.xview)
        self.excel_data_tree.configure(yscrollcommand=excel_scrollbar_y.set, xscrollcommand=excel_scrollbar_x.set)

        # 布局表格和滚动条
        self.excel_data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        excel_scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        excel_scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)

        # 配置现代化表格样式
        style = ttk.Style()
        style.configure("Treeview",
                       font=('Microsoft YaHei UI', 11),
                       background='#ffffff',
                       foreground='#2c3e50',
                       rowheight=30,
                       borderwidth=1,
                       relief='solid')
        style.configure("Treeview.Heading",
                       font=('Microsoft YaHei UI', 12, 'bold'),
                       background='#3498db',
                       foreground='#ffffff',
                       borderwidth=0,
                       relief='flat')
        style.map("Treeview",
                 background=[('selected', '#3498db')],
                 foreground=[('selected', '#ffffff')])

        # 🔧 设置表格自动调整列宽
        def on_treeview_configure(event):
            """表格大小变化时自动调整列宽"""
            _ = event  # 忽略未使用的参数
            try:
                total_width = self.excel_data_tree.winfo_width()
                if total_width > 100:  # 确保窗口已经显示
                    # 按比例分配列宽，确保"结果"和"最后更新时间"列有足够空间
                    col_ratios = {"序号": 0.08, "手机号码": 0.22, "状态": 0.12, "结果": 0.28, "最后更新时间": 0.30}
                    for col, ratio in col_ratios.items():
                        new_width = int(total_width * ratio)
                        self.excel_data_tree.column(col, width=new_width)
            except Exception:
                pass

        self.excel_data_tree.bind('<Configure>', on_treeview_configure)

        # 🔧 初始化Excel数据显示
        self.refresh_excel_data()

    def refresh_excel_data(self):
        """🔧 新增：刷新Excel数据显示"""
        try:
            # 清空现有数据
            for item in self.excel_data_tree.get_children():
                self.excel_data_tree.delete(item)

            # 获取Excel文件路径
            excel_file = self.excel_path_var.get()
            if not excel_file or not os.path.exists(excel_file):
                self.excel_stats_label.config(text="Excel文件未选择或不存在")
                return

            # 使用data_manager读取Excel数据
            try:
                from modules.data_manager import DataManager
                data_manager = DataManager(excel_file)
                contacts_data = data_manager.load_phone_numbers()

                if not contacts_data:
                    self.excel_stats_label.config(text="Excel文件中无有效数据")
                    return

                # 统计数据
                total_count = len(contacts_data)
                success_count = 0
                failed_count = 0
                pending_count = 0

                # 填充表格数据
                for index, contact in enumerate(contacts_data, 1):
                    phone = contact.get('phone', '')
                    status = contact.get('status', '待处理')

                    # 🔧 修复字段映射：Excel中的字段名与GUI期望的不同
                    # Excel中使用 'message' 字段存储结果，GUI期望 'result'
                    result = contact.get('result', '') or contact.get('message', '')
                    # Excel中使用 'timestamp' 字段存储时间，GUI期望 'update_time'
                    update_time = contact.get('update_time', '') or contact.get('timestamp', '')

                    # 🔧 优化数据显示格式
                    # 确保结果字段有默认值
                    if not result or result.strip() == '':
                        result = '未处理'

                    # 格式化更新时间
                    if not update_time or update_time.strip() == '':
                        update_time = '未更新'
                    elif isinstance(update_time, str) and len(update_time) > 19:
                        # 如果时间字符串太长，截取前19个字符（YYYY-MM-DD HH:MM:SS）
                        update_time = update_time[:19]

                    # 统计状态
                    if status == '成功':
                        success_count += 1
                    elif status in ['失败', '错误']:
                        failed_count += 1
                    else:
                        pending_count += 1

                    # 插入数据到表格
                    self.excel_data_tree.insert('', 'end', values=(
                        str(index),
                        str(phone),
                        str(status),
                        str(result),
                        str(update_time)
                    ))

                # 更新统计标签
                stats_text = f"总计: {total_count} | 成功: {success_count} | 失败: {failed_count} | 待处理: {pending_count}"
                self.excel_stats_label.config(text=stats_text)

                self.log_message("INFO", f"✅ Excel数据刷新完成：{stats_text}")

            except Exception as e:
                self.log_message("ERROR", f"❌ 读取Excel数据失败: {e}")
                self.excel_stats_label.config(text=f"读取失败: {str(e)}")

        except Exception as e:
            self.log_message("ERROR", f"❌ 刷新Excel数据异常: {e}")

    def create_logs_tab(self):
        """创建日志标签页"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="📝 系统日志")

        # 设置日志页面背景
        logs_frame.configure(style='Main.TFrame')

        # 日志控制面板
        log_control_frame = ttk.LabelFrame(logs_frame, text="🎛️ 日志控制", padding=15)
        log_control_frame.pack(fill=tk.X, padx=15, pady=15)

        # 左侧控制区域
        left_controls = ttk.Frame(log_control_frame)
        left_controls.pack(side=tk.LEFT, fill=tk.Y)

        # 日志级别选择
        level_frame = ttk.Frame(left_controls)
        level_frame.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(level_frame, text="📊 日志级别:", style='Medium.TLabel').pack(side=tk.LEFT, padx=(0, 8))
        self.log_level_var = tk.StringVar(value="INFO")
        log_level_combo = ttk.Combobox(
            level_frame, textvariable=self.log_level_var,
            values=["DEBUG", "INFO", "WARNING", "ERROR"],
            state="readonly", width=12, font=('Microsoft YaHei UI', 11)
        )
        log_level_combo.pack(side=tk.LEFT)

        # 操作按钮
        button_frame = ttk.Frame(left_controls)
        button_frame.pack(side=tk.LEFT, padx=(20, 0))

        clear_log_btn = ttk.Button(
            button_frame, text="🗑️ 清空日志",
            command=self.clear_logs,
            style='Medium.TButton'
        )
        clear_log_btn.pack(side=tk.LEFT, padx=(0, 10))

        save_log_btn = ttk.Button(
            button_frame, text="💾 保存日志",
            command=self.save_logs,
            style='Medium.TButton'
        )
        save_log_btn.pack(side=tk.LEFT)

        # 右侧选项区域
        right_controls = ttk.Frame(log_control_frame)
        right_controls.pack(side=tk.RIGHT, fill=tk.Y)

        # 自动滚动选项
        self.auto_scroll_var = tk.BooleanVar(value=True)
        auto_scroll_cb = ttk.Checkbutton(
            right_controls, text="📜 自动滚动",
            variable=self.auto_scroll_var,
            style='Medium.TCheckbutton'
        )
        auto_scroll_cb.pack(side=tk.RIGHT)

        # 日志显示区域
        log_display_frame = ttk.LabelFrame(logs_frame, text="📋 日志内容", padding=10)
        log_display_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        self.log_text = scrolledtext.ScrolledText(
            log_display_frame, wrap=tk.WORD, height=22,
            font=('Consolas', 11),
            background='#ffffff',
            foreground='#2c3e50',
            selectbackground='#3498db',
            selectforeground='#ffffff',
            borderwidth=1,
            relief='solid'
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 配置现代化日志颜色
        self.log_text.tag_configure("INFO", foreground="#2c3e50", font=('Consolas', 11))
        self.log_text.tag_configure("WARNING", foreground="#f39c12", font=('Consolas', 11, 'bold'))
        self.log_text.tag_configure("ERROR", foreground="#e74c3c", font=('Consolas', 11, 'bold'))
        self.log_text.tag_configure("DEBUG", foreground="#95a5a6", font=('Consolas', 10))
        self.log_text.tag_configure("SUCCESS", foreground="#27ae60", font=('Consolas', 11, 'bold'))

    def create_config_tab(self):
        """创建配置标签页"""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="⚙️ 系统配置")

        # 设置配置页面背景
        config_frame.configure(style='Main.TFrame')

        # 配置控制面板
        config_control_frame = ttk.LabelFrame(config_frame, text="🎛️ 配置管理", padding=15)
        config_control_frame.pack(fill=tk.X, padx=15, pady=15)

        # 配置操作按钮
        button_container = ttk.Frame(config_control_frame)
        button_container.pack(fill=tk.X)

        load_config_btn = ttk.Button(
            button_container, text="📂 加载配置",
            command=self.load_configuration,
            style='Medium.TButton'
        )
        load_config_btn.pack(side=tk.LEFT, padx=(0, 10))

        save_config_btn = ttk.Button(
            button_container, text="💾 保存配置",
            command=self.save_configuration,
            style='Medium.TButton'
        )
        save_config_btn.pack(side=tk.LEFT, padx=(0, 10))

        reset_config_btn = ttk.Button(
            button_container, text="🔄 重置为默认",
            command=self.reset_configuration,
            style='Medium.TButton'
        )
        reset_config_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 验证按钮
        validate_config_btn = ttk.Button(
            button_container, text="✅验证配置",
            command=self.validate_configuration,
            style='Medium.TButton'
        )
        validate_config_btn.pack(side=tk.RIGHT)

        # 配置编辑器区域
        editor_frame = ttk.LabelFrame(config_frame, text="📝 配置编辑器", padding=10)
        editor_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # 配置文本编辑器 - 移除阴影效果
        self.config_text = scrolledtext.ScrolledText(
            editor_frame, wrap=tk.WORD, height=28,
            font=('Consolas', 11),
            background='#ffffff',
            foreground='#2c3e50',
            selectbackground='#3498db',
            selectforeground='#ffffff',
            insertbackground='#3498db',
            borderwidth=1,
            relief='solid'
        )
        self.config_text.pack(fill=tk.BOTH, expand=True)

    def create_status_bar(self):
        """创建状态栏"""
        # 现代化状态栏
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=(0, 10))

        # 状态栏背景
        status_bg = ttk.Frame(self.status_bar)
        status_bg.pack(fill=tk.X, pady=5)

        # 状态信息区域
        status_left = ttk.Frame(status_bg)
        status_left.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 状态图标和文字
        status_icon = ttk.Label(status_left, text="💡", font=('Microsoft YaHei UI', 12))
        status_icon.pack(side=tk.LEFT, padx=(10, 5))

        self.status_text = ttk.Label(
            status_left, text="系统就绪",
            font=('Microsoft YaHei UI', 11), foreground='#2c3e50'
        )
        self.status_text.pack(side=tk.LEFT, anchor=tk.W)

        # 运行时长显示区域（替代重复的时间显示）
        runtime_frame = ttk.Frame(status_bg)
        runtime_frame.pack(side=tk.RIGHT)

        runtime_icon = ttk.Label(runtime_frame, text="⏱️", font=('Microsoft YaHei UI', 12))
        runtime_icon.pack(side=tk.LEFT, padx=(5, 5))

        self.runtime_label = ttk.Label(
            runtime_frame, text="运行时长: 00:00:00",
            font=('Microsoft YaHei UI', 11), foreground='#2c3e50'
        )
        self.runtime_label.pack(side=tk.LEFT, padx=(0, 10))

        # 更新时间
        self.update_time()

    def setup_logging(self):
        """设置日志处理"""
        # 创建自定义日志处理器
        self.log_handler = GUILogHandler(self.message_queue)
        self.log_handler.setLevel(logging.INFO)

        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.log_handler.setFormatter(formatter)

        # 添加到根日志记录器
        logging.getLogger().addHandler(self.log_handler)

    # ==================== 核心功能方法 ====================

    def start_automation(self):
        """开始自动化流程 - 增强版，包含时间段验证"""
        if self.is_running:
            messagebox.showwarning("警告", "自动化流程已在运行中")
            return

        try:
            # 🔧 首要验证：时间段检查
            beijing_tz = timezone(timedelta(hours=8))
            current_time = datetime.now(beijing_tz).strftime('%H:%M')
            time_status = self._check_current_time_status(current_time)

            if not time_status['allowed']:
                next_time = time_status.get('next_allowed_time', '未知')
                error_msg = f"当前时间 {current_time} 不在设定的执行时间段内！\n\n"
                error_msg += f"下次允许执行时间: {next_time}\n\n"
                error_msg += "请检查运行参数配置中的时间段设置。"

                messagebox.showerror("时间段限制", error_msg)
                self.log_message("ERROR", f"启动失败：当前时间 {current_time} 不在允许的执行时间段内")
                self.log_message("ERROR", f"下次允许执行时间: {next_time}")
                return

            # 验证配置
            if not self.validate_before_start():
                return

            self.log_message("INFO", f"时间段验证通过：当前时间 {current_time} 在允许的执行时间段内")

            # 更新界面状态
            self.is_running = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.NORMAL)
            self.status_indicator.config(text="🟡 运行中", style='Warning.TLabel')
            self.update_status("正在启动自动化流程...")

            # 启动自动化线程
            self.automation_thread = threading.Thread(
                target=self.run_automation_thread,
                daemon=True
            )
            self.automation_thread.start()

            self.log_message("INFO", "自动化流程已启动")

        except Exception as e:
            self.log_message("ERROR", f"启动自动化流程失败: {e}")
            self.reset_ui_state()

    def stop_automation(self):
        """停止自动化流程 - 增强版，确保完全停止所有组件"""
        if not self.is_running:
            return

        try:
            self.log_message("INFO", "用户请求停止自动化流程...")
            self.update_status("正在停止自动化流程...")

            # 设置停止标志
            self.stop_requested = True
            self.is_running = False
            self.is_paused = False

            # 通知控制器停止
            if self.controller:
                self.log_message("INFO", "通知控制器停止执行...")
                self.controller.request_stop()
                # 🔧 确保控制器也退出暂停状态
                if hasattr(self.controller, 'request_resume'):
                    self.controller.request_resume()

                # 强制停止控制器内部的所有组件
                self._force_stop_controller_components()

            # 等待线程结束（缩短等待时间到1秒，提高响应速度）
            if self.automation_thread and self.automation_thread.is_alive():
                self.log_message("INFO", "等待后台线程结束...")
                self.automation_thread.join(timeout=1.0)

                # 如果线程仍在运行，实施强制停止
                if self.automation_thread.is_alive():
                    self.log_message("WARNING", "后台线程未能在1秒内结束，实施强制停止")
                    self._force_stop_thread()

            # 强制清理所有相关进程
            self._cleanup_all_processes()

            self.reset_ui_state()
            self.log_message("INFO", "自动化流程已完全停止")

        except Exception as e:
            self.log_message("ERROR", f"停止自动化流程失败: {e}")
            self.reset_ui_state()

    def _force_stop_thread(self):
        """强制停止自动化线程"""
        try:
            import ctypes

            if self.automation_thread and self.automation_thread.is_alive():
                self.log_message("WARNING", "执行强制线程终止...")

                # 获取线程ID
                thread_id = self.automation_thread.ident
                if thread_id:
                    # 强制终止线程（仅在Windows上）
                    try:
                        ctypes.pythonapi.PyThreadState_SetAsyncExc(
                            ctypes.c_long(thread_id),
                            ctypes.py_object(SystemExit)
                        )
                        self.log_message("INFO", "强制线程终止完成")
                    except Exception as e:
                        self.log_message("ERROR", f"强制线程终止失败: {e}")

        except Exception as e:
            self.log_message("ERROR", f"强制停止机制异常: {e}")

    def _force_stop_controller_components(self):
        """强制停止控制器内部的所有组件"""
        try:
            if not self.controller:
                return

            self.log_message("INFO", "强制停止控制器内部组件...")

            # 停止频率错误处理器
            if hasattr(self.controller, 'frequency_handler') and self.controller.frequency_handler:
                try:
                    # 设置终止标志
                    self.controller.frequency_handler.terminate_required = True
                    # 清除重启标志，防止循环
                    if hasattr(self.controller.frequency_handler, '_restart_required'):
                        self.controller.frequency_handler._restart_required = False
                    self.log_message("INFO", "频率错误处理器已停止")
                except Exception as e:
                    self.log_message("WARNING", f"停止频率错误处理器失败: {e}")

            # 停止窗口管理器
            if hasattr(self.controller, 'window_manager') and self.controller.window_manager:
                try:
                    # 调用窗口管理器的清理方法
                    self.controller.window_manager.cleanup()
                    self.log_message("INFO", "窗口管理器已清理")
                except Exception as e:
                    self.log_message("WARNING", f"清理窗口管理器失败: {e}")

            # 强制设置控制器停止标志
            self.controller._stop_requested = True
            self.controller._is_running = False

        except Exception as e:
            self.log_message("ERROR", f"强制停止控制器组件失败: {e}")

    def _cleanup_all_processes(self):
        """清理所有相关进程"""
        try:
            import psutil
            import os

            self.log_message("INFO", "开始清理相关进程...")

            # 获取当前进程ID
            current_pid = os.getpid()
            current_process = psutil.Process(current_pid)

            # 查找并终止子进程
            children = current_process.children(recursive=True)
            for child in children:
                try:
                    child_name = child.name()
                    self.log_message("INFO", f"终止子进程: {child_name} (PID: {child.pid})")
                    child.terminate()
                    child.wait(timeout=1)
                except Exception as e:
                    self.log_message("WARNING", f"终止子进程失败: {e}")

            # 查找可能卡住的Python进程（与微信自动化相关）
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] == 'python.exe' and proc.info['pid'] != current_pid:
                        cmdline = proc.info.get('cmdline', [])
                        if any('wechat' in str(arg).lower() or 'automation' in str(arg).lower() for arg in cmdline):
                            self.log_message("INFO", f"终止相关Python进程: PID {proc.info['pid']}")
                            proc.terminate()
                            proc.wait(timeout=1)
                except Exception as e:
                    continue

            self.log_message("INFO", "进程清理完成")

        except ImportError:
            self.log_message("WARNING", "psutil模块不可用，跳过进程清理")
        except Exception as e:
            self.log_message("ERROR", f"清理进程失败: {e}")

    def pause_automation(self):
        """暂停/恢复自动化流程 - 增强版，与控制器同步"""
        if not self.is_running:
            return

        try:
            if self.is_paused:
                # 恢复执行
                self.is_paused = False
                self.pause_button.config(text="⏸ 暂停")
                self.status_indicator.config(text="🟡 运行中", style='Warning.TLabel')
                self.update_status("自动化流程已恢复")
                self.log_message("INFO", "自动化流程已恢复执行")

                # 🔧 关键修复：通知控制器恢复执行
                if self.controller:
                    self.controller.request_resume()
                    self.log_message("INFO", "已通知控制器恢复执行")
            else:
                # 暂停执行
                self.is_paused = True
                self.pause_button.config(text="▶ 恢复")
                self.status_indicator.config(text="🟠 已暂停", style='Warning.TLabel')
                self.update_status("自动化流程已暂停")
                self.log_message("INFO", "自动化流程已暂停")

                # 🔧 关键修复：通知控制器暂停执行
                if self.controller:
                    self.controller.request_pause()
                    self.log_message("INFO", "已通知控制器暂停执行")

        except Exception as e:
            self.log_message("ERROR", f"暂停/恢复操作失败: {e}")

    def run_automation_thread(self):
        """在后台线程中运行自动化流程"""
        try:
            # 重置停止标志
            self.stop_requested = False

            # 检查是否被停止
            if self.stop_requested:
                self.log_message("INFO", "自动化流程在启动前被停止")
                return

            # 初始化控制器
            excel_file = self.excel_path_var.get()
            config_file = "config.json"

            # 🔧 详细的初始化状态跟踪
            self.update_status("正在初始化控制器...")
            self.update_execution_step("正在初始化：准备控制器组件")
            self.log_message("INFO", f"开始初始化控制器 - Excel文件: {excel_file}")

            # 🔧 移除强制GUI刷新，通过消息队列更新
            self.process_messages()

            # 🔧 添加超时机制防止初始化卡住
            import threading
            import time

            controller_init_result = []  # 使用列表来存储结果，支持线程间共享
            init_exception = []

            def init_controller():
                try:
                    controller_init_result.append(WeChatMainController(excel_file, config_file))
                except Exception as e:
                    init_exception.append(e)

            # 启动初始化线程
            init_thread = threading.Thread(target=init_controller, daemon=True)
            init_thread.start()

            # 等待初始化完成，最多等待30秒
            timeout = 30
            start_time = time.time()
            while init_thread.is_alive() and (time.time() - start_time) < timeout:
                # 更新状态显示
                elapsed = int(time.time() - start_time)
                self.update_status(f"正在初始化控制器... ({elapsed}s)")
                self.process_messages()
                time.sleep(0.5)

            # 检查初始化结果
            if init_thread.is_alive():
                self.log_message("ERROR", "❌ 控制器初始化超时（30秒），程序无法继续")
                self.update_status("❌ 控制器初始化超时，流程已终止")
                self.update_execution_step("❌ 初始化失败：控制器初始化超时")
                self.process_messages()
                return

            if init_exception:
                self.log_message("ERROR", f"❌ 控制器初始化异常: {init_exception[0]}")
                self.update_status("❌ 控制器初始化失败，流程已终止")
                self.update_execution_step("❌ 初始化失败：控制器初始化异常")
                self.process_messages()
                return

            if not controller_init_result:
                self.log_message("ERROR", "❌ 控制器初始化失败：未返回结果")
                self.update_status("❌ 控制器初始化失败，流程已终止")
                self.update_execution_step("❌ 初始化失败：控制器初始化未完成")
                self.process_messages()
                return

            self.controller = controller_init_result[0]

            # 初始化完成确认
            self.update_status("控制器初始化完成")
            self.update_execution_step("初始化完成：控制器组件就绪")
            self.log_message("INFO", "✅ 控制器初始化成功")

            # 🔧 建立GUI与控制器的进度更新连接
            if self.controller:
                self.controller.set_gui_callback(self.update_progress_data)
                self.controller.set_step_callback(self.update_execution_step)
                self.controller.set_window_detection_callback(self.update_window_detection)
                self.controller.set_countdown_callback(self.update_countdown_display)
            else:
                self.log_message("ERROR", "❌ 控制器为空，无法建立回调连接")
                return

            # 立即应用GUI参数到控制器
            self.apply_params_to_controller()
            self.log_message("INFO", "GUI参数已应用到控制器")

            # 检查是否被停止
            if self.stop_requested:
                self.log_message("INFO", "自动化流程在初始化后被停止")
                return

            # 获取微信窗口
            self.update_execution_step("正在执行第一步：扫描微信窗口")
            self.update_status("正在扫描微信窗口...")
            self.update_window_detection("扫描中...")
            if not self.controller:
                self.log_message("ERROR", "控制器初始化失败")
                self.update_window_detection("扫描失败")
                self.update_execution_step("第一步失败：控制器初始化失败")
                return
            windows = self.controller.get_wechat_windows()

            if not windows:
                self.log_message("ERROR", "❌ 未找到微信窗口，程序无法继续执行")
                self.update_window_detection("❌ 未找到窗口")
                self.update_execution_step("❌ 第一步失败：未找到微信窗口")
                self.update_status("❌ 未找到微信窗口，流程已终止")

                # 🔧 通过消息队列更新GUI显示
                self.process_messages()  # 立即处理消息队列

                self.log_message("ERROR", "🚨 请确保微信程序已启动并且窗口可见")
                self.log_message("ERROR", "🚨 程序将停止执行，请启动微信后重试")
                return

            # 更新窗口检测状态
            self.update_window_detection(f"已检测到 {len(windows)} 个窗口")
            self.update_execution_step(f"第一步完成：成功检测到 {len(windows)} 个微信窗口")

            # 检查是否被停止
            if self.stop_requested:
                self.log_message("INFO", "自动化流程在扫描窗口后被停止")
                return

            # 加载联系人数据
            self.update_execution_step("正在执行第二步：加载联系人数据")
            self.update_status("正在加载联系人数据...")
            contacts = self.controller.load_contacts_data()

            if not contacts:
                self.log_message("ERROR", "未找到待处理的联系人")
                self.update_execution_step("第二步失败：未找到待处理的联系人")
                return

            self.update_execution_step(f"第二步完成：成功加载 {len(contacts)} 个联系人")

            # 检查是否被停止
            if self.stop_requested:
                self.log_message("INFO", "自动化流程在加载数据后被停止")
                return

            # 更新统计信息
            self.execution_stats.update(self.controller.execution_stats)
            self.update_statistics_display()

            # 移动所有窗口到指定位置
            self.update_execution_step("正在执行第三步：移动微信窗口到指定位置")
            self.update_status("正在移动微信窗口...")
            if not self.controller.move_all_windows_to_target_position(windows):
                self.log_message("WARNING", "窗口移动失败，但继续执行")
                self.update_execution_step("第三步警告：窗口移动失败，但继续执行")
            else:
                self.update_execution_step("第三步完成：所有微信窗口已移动到指定位置")

            # 检查是否被停止
            if self.stop_requested:
                self.log_message("INFO", "自动化流程在移动窗口后被停止")
                return

            # 执行多窗口流程（带暂停和停止检查）
            self.update_execution_step("正在执行第四步：启动多窗口自动化流程")
            self.update_status("正在执行自动化流程...")
            result = self._execute_with_pause_check(windows, contacts)

            if self.stop_requested:
                self.log_message("INFO", "自动化流程被用户停止")
                self.update_status("自动化流程被用户停止")
                self.update_execution_step("第四步中断：用户手动停止自动化流程")
            elif result:
                self.log_message("INFO", "自动化流程执行完成")
                self.update_status("自动化流程执行完成")
                self.update_execution_step("第四步完成：多窗口自动化流程执行成功")
            else:
                self.log_message("ERROR", "自动化流程执行失败")
                self.update_status("自动化流程执行失败")
                self.update_execution_step("第四步失败：多窗口自动化流程执行失败")

        except Exception as e:
            self.log_message("ERROR", f"自动化流程异常: {e}")
            self.update_status(f"自动化流程异常: {e}")
        finally:
            self.reset_ui_state()

    def _execute_with_pause_check(self, windows, contacts):
        """执行自动化流程，支持暂停和停止检查"""
        try:
            # 检查控制器是否存在
            if not self.controller:
                self.log_message("ERROR", "控制器未初始化")
                return False

            # 检查停止状态
            if self.stop_requested:
                self.log_message("INFO", "执行前检测到停止请求")
                return False

            # 启动控制器执行，控制器内部会检查停止标志
            self.log_message("INFO", "开始执行自动化流程...")
            result = self.controller.execute_multi_window_flow(windows, contacts)

            # 检查执行结果
            if self.stop_requested:
                self.log_message("INFO", "执行过程中检测到停止请求")
                return False

            return result

        except Exception as e:
            self.log_message("ERROR", f"执行过程中发生异常: {e}")
            return False

    def validate_before_start(self) -> bool:
        """启动前验证"""
        # 检查Excel文件
        excel_file = self.excel_path_var.get()
        if not excel_file or not Path(excel_file).exists():
            messagebox.showerror("错误", "请选择有效的Excel文件")
            return False

        # 检查配置文件
        if not Path("config.json").exists():
            messagebox.showerror("错误", "配置文件config.json不存在")
            return False

        return True

    def reset_ui_state(self):
        """重置界面状态 - 增强版，确保完全重置暂停状态"""
        self.is_running = False
        self.is_paused = False
        self.stop_requested = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.pause_button.config(state=tk.DISABLED, text="⏸ 暂停")
        self.status_indicator.config(text="🟢 系统就绪", style='Success.TLabel')

        # 重置执行步骤和窗口检测状态
        self.update_execution_step("待启动")
        self.update_window_detection("未检测")

        # 🔧 确保控制器也重置暂停状态
        if self.controller:
            if hasattr(self.controller, 'request_resume'):
                self.controller.request_resume()
            if hasattr(self.controller, '_is_paused'):
                self.controller._is_paused = False

        # 🔧 重置主进度条显示
        self.main_progress_bar.config(value=0)
        self.main_progress_percent.config(text="0%")

        # 🔧 重置运行状态显示
        if hasattr(self, 'running_status_label'):
            self.running_status_label.config(text="⏸️ 系统待机", foreground='#7f8c8d')
        if hasattr(self, 'window_status_label'):
            self.window_status_label.config(text="🔍 窗口检测：待检测", foreground='#95a5a6')

    # ==================== 界面更新方法 ====================

    def should_update_gui(self) -> bool:
        """🔧 检查是否应该更新GUI - 防抖机制"""
        current_time = time.time()
        if current_time - self.last_update_time < self.update_throttle_interval:
            return False
        return True

    def mark_update_needed(self, update_type: str):
        """🔧 标记需要更新的类型"""
        if update_type in self.pending_updates:
            self.pending_updates[update_type] = True

    def has_data_changed(self, new_stats: dict) -> bool:
        """🔧 检测数据是否实际发生变化 - 增强版，更精确的变化检测"""
        # 检查关键数据字段
        key_fields = ['total_contacts', 'processed_contacts', 'successful_adds', 'failed_adds', 'total_windows', 'completed_windows']

        for key in key_fields:
            old_value = self.last_displayed_stats.get(key, 0)
            new_value = new_stats.get(key, 0)
            if old_value != new_value:
                return True

        # 检查状态字段
        status_fields = ['start_time', 'end_time']
        for key in status_fields:
            old_value = self.last_displayed_stats.get(key)
            new_value = new_stats.get(key)
            if old_value != new_value:
                return True

        return False

    def _accumulate_stats_data(self, new_stats: dict):
        """🔧 修复：直接使用绝对值更新统计数据，避免重复累加"""
        try:
            # 🔧 关键修复：直接使用传入的绝对值，不再进行累加操作
            # 因为main_controller已经处理了累加逻辑，GUI只需要显示最终结果
            update_fields = ['processed_contacts', 'successful_adds', 'failed_adds', 'skipped_contacts']

            for field in update_fields:
                if field in new_stats:
                    # 直接使用新值，不再累加
                    self.execution_stats[field] = new_stats.get(field, 0)

            # 直接更新非累加字段
            direct_update_fields = ['total_contacts', 'total_windows', 'completed_windows', 'start_time', 'end_time']
            for field in direct_update_fields:
                if field in new_stats:
                    self.execution_stats[field] = new_stats[field]

        except Exception as e:
            self.log_message("ERROR", f"❌ 数据更新失败: {e}")

    def update_status(self, message: str):
        """更新状态信息"""
        self.message_queue.put(('status', message))

    def update_execution_step(self, step: str):
        """更新执行步骤显示 - 增强版，避免重复显示"""
        # 🔧 检查是否为重复的步骤消息
        if step == self.last_step_message:
            return  # 跳过重复的步骤消息

        # 更新最后步骤消息
        self.last_step_message = step
        self.message_queue.put(('step', step))

    def update_window_detection(self, status: str):
        """更新微信窗口检测状态 - 重构版"""
        # 🔧 新版本：直接更新窗口状态标签
        if hasattr(self, 'window_status_label'):
            self.window_status_label.config(text=f"🔍 窗口检测：{status}")

        # 保留消息队列机制以兼容其他组件
        self.message_queue.put(('window', status))

    def log_message(self, level: str, message: str):
        """添加日志消息 - 增强版，智能去重机制"""
        # 🔧 智能日志去重检查 - 基于消息内容而非完整匹配
        message_core = self._extract_log_core(message)
        log_key = f"{level}:{message_core}"

        # 检查是否为重复日志
        if log_key in self.recent_logs:
            return  # 跳过重复日志

        # 添加到最近日志列表
        self.recent_logs.append(log_key)

        # 保持最近日志列表大小
        if len(self.recent_logs) > self.max_recent_logs:
            self.recent_logs.pop(0)  # 移除最旧的日志

        self.message_queue.put(('log', level, message))

    def _extract_log_core(self, message: str) -> str:
        """🔧 提取日志核心内容，用于智能去重"""
        # 移除时间戳、数字变化等动态内容
        import re

        # 移除常见的动态内容模式
        patterns_to_remove = [
            r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}',  # 时间戳
            r'\(\d+s\)',  # 秒数
            r'\d+/\d+',   # 进度数字
            r'\d+%',      # 百分比
            r'第\s*\d+\s*个',  # 序号
            r'\d+\s*秒',   # 倒计时
        ]

        core_message = message
        for pattern in patterns_to_remove:
            core_message = re.sub(pattern, '[DYNAMIC]', core_message)

        return core_message.strip()

    def update_all_displays(self):
        """🔧 统一的GUI更新方法 - 修复闪屏和卡顿问题"""
        with self.update_lock:
            try:
                current_time = time.time()

                # 检查是否需要更新
                if not self.should_update_gui():
                    return

                # 检查是否有待处理的更新
                has_pending = any(self.pending_updates.values())
                if not has_pending:
                    return

                # 批量更新所有显示
                if self.pending_updates["statistics"]:
                    self.update_statistics_display()
                    self.pending_updates["statistics"] = False

                if self.pending_updates["status"]:
                    self.update_status_display()
                    self.pending_updates["status"] = False

                # 更新时间戳
                self.last_update_time = current_time

                # 🔧 彻底移除强制GUI刷新，完全依赖防抖机制
                # 界面更新通过Tkinter的自然刷新机制进行，避免强制刷新导致闪烁

            except Exception as e:
                self.log_message("ERROR", f"❌ 统一GUI更新失败: {e}")

    def update_progress_data(self, stats_data: dict):
        """🔧 更新进度数据 - 线程安全的进度更新方法，优化版"""
        try:
            # 🔧 数据验证
            if not isinstance(stats_data, dict) or not stats_data:
                return

            # 检查数据是否实际发生变化
            if not self.has_data_changed(stats_data):
                return

            # 将进度更新消息放入队列，由主线程处理
            self.message_queue.put(('progress_update', stats_data))

        except Exception as e:
            self.log_message("ERROR", f"❌ 进度数据更新失败: {e}")

    def animate_progress_bar(self, progress_bar, target_value, duration=500):
        """🔧 进度条动画效果 - 平滑过渡到目标值"""
        try:
            current_value = progress_bar['value']
            if abs(current_value - target_value) < 0.1:
                return

            steps = 20  # 动画步数
            step_size = (target_value - current_value) / steps
            step_duration = max(duration // steps, 10)  # 最小10ms间隔

            def animate_step(step):
                if step <= steps:
                    new_value = current_value + (step_size * step)
                    progress_bar.config(value=new_value)
                    if step < steps:
                        self.root.after(step_duration, lambda: animate_step(step + 1))
                    else:
                        progress_bar.config(value=target_value)

            animate_step(1)

        except Exception:
            # 如果动画失败，直接设置目标值
            progress_bar.config(value=target_value)

    def update_statistics_display(self):
        """🔧 更新统计显示 - 重构版：基于Excel数据"""
        try:
            # 🔧 新版本：更新运行状态显示
            if hasattr(self, 'running_status_label'):
                if self.is_running:
                    self.running_status_label.config(text="▶️ 正在运行", foreground='#27ae60')
                elif self.is_paused:
                    self.running_status_label.config(text="⏸️ 已暂停", foreground='#f39c12')
                else:
                    self.running_status_label.config(text="⏸️ 系统待机", foreground='#7f8c8d')

            # 🔧 新版本：刷新Excel数据显示
            if hasattr(self, 'excel_data_tree'):
                self.refresh_excel_data()

        except Exception as e:
            self.log_message("ERROR", f"❌ 更新统计显示失败: {e}")

        # 🔧 删除旧的统计表格更新逻辑，已替换为Excel数据显示

    def process_messages(self):
        """处理消息队列"""
        try:
            while True:
                message = self.message_queue.get_nowait()

                if message[0] == 'status':
                    self.status_text.config(text=message[1])
                    self.current_status_label.config(text=f"系统状态: {message[1]}")

                elif message[0] == 'step':
                    # 更新执行步骤显示
                    self.current_step_label.config(text=f"执行步骤: {message[1]}")

                elif message[0] == 'window':
                    # 🔧 更新微信窗口检测状态 - 新版本
                    if hasattr(self, 'window_status_label'):
                        self.window_status_label.config(text=f"🔍 窗口检测：{message[1]}")
                    # 兼容旧版本
                    if hasattr(self, 'window_detection_label'):
                        self.window_detection_label.config(text=f"微信窗口: {message[1]}")

                elif message[0] == 'log':
                    level, text = message[1], message[2]

                    # 🔧 优化：减少DEBUG日志的显示频率，提升性能
                    if level == "DEBUG" and not self.is_running:
                        continue  # 非运行状态下跳过DEBUG日志

                    timestamp = datetime.now().strftime("%H:%M:%S")
                    log_line = f"[{timestamp}] {level}: {text}\n"

                    self.log_text.insert(tk.END, log_line, level)

                    if self.auto_scroll_var.get():
                        self.log_text.see(tk.END)

                elif message[0] == 'progress_update':
                    # 🔧 处理进度更新消息 - 重构版，实现真正的数据累加
                    stats_data = message[1]

                    # 🔧 实现数据累加而非覆盖
                    self._accumulate_stats_data(stats_data)

                    # 检查数据是否实际发生变化
                    if self.has_data_changed(self.execution_stats):
                        # 标记需要更新
                        self.mark_update_needed("statistics")
                        self.mark_update_needed("status")

                        # 更新缓存的显示数据
                        self.last_displayed_stats.update(self.execution_stats)

                        # 触发统一更新（带防抖）
                        self.update_all_displays()

        except queue.Empty:
            pass
        finally:
            # 🔧 优化：进一步降低消息队列检查频率，彻底消除闪烁
            # 运行时500ms检查一次，空闲时2000ms检查一次，确保界面稳定
            check_interval = 500 if self.is_running else 2000
            self.root.after(check_interval, self.process_messages)

    def update_time(self):
        """更新时间显示 - 显示准确的北京时间（UTC+8）并检查时间段状态"""
        # 获取北京时间（UTC+8）
        beijing_tz = timezone(timedelta(hours=8))
        beijing_time = datetime.now(beijing_tz)

        # 格式化时间字符串
        time_str = beijing_time.strftime("%Y-%m-%d %H:%M:%S")
        current_time_only = beijing_time.strftime("%H:%M")

        # 🔧 增强：检查当前时间是否在允许的时间段内
        time_status = self._check_current_time_status(current_time_only)

        # 更新工具栏的主时间显示，包含时间段状态
        if hasattr(self, 'main_time_label'):
            display_text = f"{time_str} {time_status['indicator']}"
            self.main_time_label.config(text=display_text)

        # 🔧 增强：更新状态指示器，显示时间段状态
        if hasattr(self, 'status_indicator'):
            if not self.is_running:
                if time_status['allowed']:
                    self.status_indicator.config(text="🟢 系统就绪 (时间段内)", style='Success.TLabel')
                else:
                    self.status_indicator.config(text="🔴 等待时间段", style='Error.TLabel')

        # 更新状态栏的运行时长显示
        if hasattr(self, 'runtime_label'):
            if self.is_running and hasattr(self, 'execution_stats') and self.execution_stats.get('start_time'):
                start_time = self.execution_stats['start_time']
                current_time = beijing_time.replace(tzinfo=None)  # 移除时区信息进行计算
                if isinstance(start_time, str):
                    start_time = datetime.fromisoformat(start_time)
                runtime_delta = current_time - start_time
                runtime_str = str(runtime_delta).split('.')[0]  # 移除微秒
                self.runtime_label.config(text=f"运行时长: {runtime_str}")
            else:
                if time_status['allowed']:
                    self.runtime_label.config(text="运行时长: 00:00:00")
                else:
                    next_time = time_status.get('next_allowed_time', '未知')
                    self.runtime_label.config(text=f"下次执行: {next_time}")

        # 每秒更新一次
        self.root.after(1000, self.update_time)

    def _check_current_time_status(self, current_time_str: str) -> dict:
        """检查当前时间状态 - 用于GUI显示"""
        try:
            # 检查上午时段
            morning_enabled = self.time_slot_enabled["morning_enabled"].get()
            morning_start = self.runtime_params["morning_start"].get()
            morning_end = self.runtime_params["morning_end"].get()

            # 检查下午时段
            afternoon_enabled = self.time_slot_enabled["afternoon_enabled"].get()
            afternoon_start = self.runtime_params["afternoon_start"].get()
            afternoon_end = self.runtime_params["afternoon_end"].get()

            # 解析当前时间
            current_hour, current_minute = map(int, current_time_str.split(':'))
            current_minutes = current_hour * 60 + current_minute

            # 检查是否在允许的时间段内
            in_allowed_time = False
            next_allowed_time = "未设置"

            # 检查上午时段
            if morning_enabled and morning_start and morning_end:
                start_hour, start_minute = map(int, morning_start.split(':'))
                end_hour, end_minute = map(int, morning_end.split(':'))
                start_minutes = start_hour * 60 + start_minute
                end_minutes = end_hour * 60 + end_minute

                if start_minutes <= current_minutes <= end_minutes:
                    in_allowed_time = True
                elif current_minutes < start_minutes:
                    next_allowed_time = morning_start

            # 检查下午时段
            if not in_allowed_time and afternoon_enabled and afternoon_start and afternoon_end:
                start_hour, start_minute = map(int, afternoon_start.split(':'))
                end_hour, end_minute = map(int, afternoon_end.split(':'))
                start_minutes = start_hour * 60 + start_minute
                end_minutes = end_hour * 60 + end_minute

                if start_minutes <= current_minutes <= end_minutes:
                    in_allowed_time = True
                elif current_minutes < start_minutes and (next_allowed_time == "未设置" or start_minutes < int(next_allowed_time.split(':')[0]) * 60 + int(next_allowed_time.split(':')[1])):
                    next_allowed_time = afternoon_start

            # 返回状态信息
            if in_allowed_time:
                return {
                    "allowed": True,
                    "indicator": "✅",
                    "next_allowed_time": None
                }
            else:
                return {
                    "allowed": False,
                    "indicator": "⏰",
                    "next_allowed_time": next_allowed_time
                }

        except Exception:
            # 出错时返回安全状态
            return {
                "allowed": False,
                "indicator": "❓",
                "next_allowed_time": "检查失败"
            }

    # ==================== 文件操作方法 ====================

    def browse_excel_file(self):
        """浏览Excel文件"""
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.excel_path_var.set(filename)

    def open_config_file(self):
        """打开配置文件"""
        try:
            import os
            os.startfile("config.json")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开配置文件: {e}")

    def open_excel_file(self):
        """打开Excel文件"""
        try:
            import os
            excel_file = self.excel_path_var.get()
            if excel_file and Path(excel_file).exists():
                os.startfile(excel_file)
            else:
                messagebox.showerror("错误", "Excel文件不存在")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开Excel文件: {e}")

    def load_configuration(self):
        """加载配置"""
        try:
            # 🔧 增强的配置文件读取，支持多种编码和错误处理
            config_data = None
            encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'latin1']

            for encoding in encodings:
                try:
                    with open("config.json", "r", encoding=encoding) as f:
                        config_data = f.read().strip()
                        if not config_data:
                            self.log_message("WARNING", "配置文件为空，显示默认配置")
                            config_data = "{}"
                        break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    if encoding == encodings[-1]:
                        raise e
                    continue

            if config_data is None:
                self.log_message("WARNING", "无法读取配置文件，显示默认配置")
                config_data = "{}"

            self.config_text.delete(1.0, tk.END)
            self.config_text.insert(1.0, config_data)

            # 基础参数已移除，配置文件仅用于显示，不再解析特定参数

            self.log_message("INFO", "配置文件加载成功")

        except Exception as e:
            self.log_message("ERROR", f"加载配置文件失败: {e}")
            # 显示默认配置
            self.config_text.delete(1.0, tk.END)
            self.config_text.insert(1.0, "{}")

    def save_configuration(self):
        """保存配置"""
        try:
            config_data = self.config_text.get(1.0, tk.END).strip()

            # 验证JSON格式
            json.loads(config_data)

            with open("config.json", "w", encoding="utf-8") as f:
                f.write(config_data)

            self.log_message("INFO", "配置文件保存成功")
            messagebox.showinfo("成功", "配置文件保存成功")

        except json.JSONDecodeError as e:
            messagebox.showerror("错误", f"配置文件格式错误: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置文件失败: {e}")

    def reset_configuration(self):
        """重置配置为默认值"""
        if messagebox.askyesno("确认", "确定要重置配置为默认值吗？"):
            try:
                # 这里可以加载默认配置
                self.load_configuration()
                self.log_message("INFO", "配置已重置为默认值")
            except Exception as e:
                self.log_message("ERROR", f"重置配置失败: {e}")

    # ==================== 工具方法 ====================

    def validate_configuration(self):
        """验证配置"""
        try:
            # 检查配置文件
            if not Path("config.json").exists():
                messagebox.showerror("错误", "配置文件不存在")
                return

            with open("config.json", "r", encoding="utf-8") as f:
                json.load(f)  # 验证JSON格式

            # 检查Excel文件
            excel_file = self.excel_path_var.get()
            if not excel_file or not Path(excel_file).exists():
                messagebox.showerror("错误", "Excel文件不存在")
                return

            # 检查必要的模块
            required_modules = ["modules", "modules.window_manager", "modules.data_manager"]
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError as e:
                    messagebox.showerror("错误", f"缺少必要模块: {module}")
                    return

            messagebox.showinfo("成功", "配置验证通过")
            self.log_message("INFO", "配置验证通过")

        except Exception as e:
            messagebox.showerror("错误", f"配置验证失败: {e}")

    def clear_logs(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("INFO", "日志已清空")

    def save_logs(self):
        """保存日志"""
        try:
            filename = filedialog.asksaveasfilename(
                title="保存日志",
                defaultextension=".log",
                filetypes=[("Log files", "*.log"), ("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                log_content = self.log_text.get(1.0, tk.END)
                with open(filename, "w", encoding="utf-8") as f:
                    f.write(log_content)

                messagebox.showinfo("成功", f"日志已保存到: {filename}")
                self.log_message("INFO", f"日志已保存到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"保存日志失败: {e}")

    def export_logs(self):
        """导出日志"""
        self.save_logs()

    def clean_logs(self):
        """清理日志文件"""
        try:
            logs_dir = Path("logs")
            if logs_dir.exists():
                # 这里可以实现日志清理逻辑
                messagebox.showinfo("信息", "日志清理功能待实现")
            else:
                messagebox.showinfo("信息", "日志目录不存在")
        except Exception as e:
            messagebox.showerror("错误", f"清理日志失败: {e}")

    def reset_statistics(self):
        """重置统计信息"""
        if messagebox.askyesno("确认", "确定要重置统计信息吗？"):
            self.execution_stats = {
                "total_contacts": 0,
                "processed_contacts": 0,
                "successful_adds": 0,
                "failed_adds": 0,
                "skipped_contacts": 0,
                "total_windows": 0,
                "completed_windows": 0,
                "start_time": None,
                "end_time": None
            }
            self.update_statistics_display()
            self.log_message("INFO", "统计信息已重置")



    def show_statistics_dialog(self):
        """显示统计对话框"""
        # 切换到数据状态标签页
        self.notebook.select(1)  # 数据状态标签页的索引

    def show_help(self):
        """显示帮助信息"""
        help_text = """
微信自动化添加好友控制台 v1.0.0

使用说明：
1. 在"控制面板"中选择Excel文件并配置运行参数
2. 在"控制面板"右侧查看实时运行状态
3. 点击"开始自动化"按钮开始执行
4. 在"数据状态"中查看详细执行进度
5. 在"日志"中查看详细执行信息

注意事项：
- 确保微信已登录并可见
- Excel文件格式必须正确
- 建议在执行前备份数据
- 遇到问题请查看日志信息

技术支持：
- 查看README.md获取详细说明
- 检查config.json配置文件
- 查看logs目录下的日志文件
        """

        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("500x400")
        help_window.resizable(False, False)

        text_widget = scrolledtext.ScrolledText(help_window, wrap=tk.WORD, padx=10, pady=10, borderwidth=1, relief='solid')
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(1.0, help_text)
        text_widget.config(state=tk.DISABLED)

    def show_about(self):
        """显示关于信息"""
        about_text = """
微信自动化添加好友控制台
版本：v1.0.0
创建时间：2025-01-28

功能特性：
• 6步骤严格执行流程
• 多窗口循环处理
• 智能错误处理
• 实时数据状态监控
• 可视化操作界面

技术架构：
• Python + tkinter
• 模块化设计
• 多线程处理
• 配置驱动

开发者：AI助手
        """
        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """窗口关闭事件 - 增强版，确保完全退出"""
        try:
            if self.is_running:
                if messagebox.askyesno("确认", "自动化流程正在运行，确定要退出吗？"):
                    self.log_message("INFO", "用户确认退出，正在停止所有进程...")
                    self.stop_automation()
                    # 强制退出，不等待
                    self.root.after(500, self._force_exit)
            else:
                self._force_exit()
        except Exception as e:
            print(f"关闭窗口异常: {e}")
            self._force_exit()

    def _force_exit(self):
        """强制退出程序"""
        try:
            import sys
            import os

            # 记录退出日志
            try:
                self.log_message("INFO", "程序正在强制退出...")
            except:
                pass

            # 强制停止所有组件
            try:
                self.stop_requested = True
                self.is_running = False
                if self.controller:
                    self.controller.request_stop()
            except:
                pass

            # 清理进程
            try:
                self._cleanup_all_processes()
            except:
                pass

            # 销毁GUI
            try:
                self.root.quit()
                self.root.destroy()
            except:
                pass

            # 强制退出Python进程
            try:
                os._exit(0)
            except:
                sys.exit(0)

        except Exception as e:
            print(f"强制退出异常: {e}")
            try:
                import os
                os._exit(1)
            except:
                pass

    # ==================== 运行参数配置方法 ====================

    def on_param_change(self, _=None):
        """参数变化时的回调函数 - 增强版：动态参数响应"""
        try:
            # 实时验证和应用参数
            self.validate_and_apply_params()

            # 🆕 动态参数响应 - 立即保存到配置文件
            self._save_params_to_config_immediately()

            # 🆕 如果控制器正在运行，立即应用新参数
            if self.controller and self.is_running:
                self.apply_params_to_controller()
                self.log_message("INFO", "运行时参数已实时更新")

        except Exception as e:
            self.log_message("WARNING", f"参数验证失败: {e}")

    def _save_params_to_config_immediately(self):
        """🆕 立即保存参数到配置文件（无用户提示）"""
        try:
            # 读取现有配置
            with open("config.json", "r", encoding="utf-8") as f:
                config = json.load(f)

            # 更新运行参数
            config["runtime_parameters"] = {
                "single_add_interval": {
                    "min": int(self.runtime_params["interval_min"].get()),
                    "max": int(self.runtime_params["interval_max"].get()),
                    "unit": "seconds",
                    "description": "单次添加间隔范围"
                },
                "daily_add_limit": {
                    "value": int(self.runtime_params["daily_limit"].get()),
                    "description": "每日添加上限"
                },
                "max_adds_per_window": {
                    "value": int(self.runtime_params["max_per_window"].get()),
                    "description": "每窗口最大添加数量"
                },
                "execution_time_slots": {
                    "morning": {
                        "start": self.runtime_params["morning_start"].get(),
                        "end": self.runtime_params["morning_end"].get(),
                        "enabled": self.time_slot_enabled["morning_enabled"].get(),
                        "description": "上午执行时段"
                    },
                    "afternoon": {
                        "start": self.runtime_params["afternoon_start"].get(),
                        "end": self.runtime_params["afternoon_end"].get(),
                        "enabled": self.time_slot_enabled["afternoon_enabled"].get(),
                        "description": "下午执行时段"
                    }
                }
            }

            # 🆕 同步更新multi_window配置，确保数量限制一致性
            max_per_window_value = int(self.runtime_params["max_per_window"].get())
            if "multi_window" not in config:
                config["multi_window"] = {}
            config["multi_window"]["contacts_per_window"] = max_per_window_value

            # 保存配置
            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            self.log_message("DEBUG", f"参数已实时同步到配置文件：每日限制={config['runtime_parameters']['daily_add_limit']['value']}，单窗口限制={max_per_window_value}")

        except Exception as e:
            self.log_message("WARNING", f"实时保存参数失败: {e}")

    def on_morning_enabled_change(self):
        """上午时段启用状态变化回调"""
        enabled = self.time_slot_enabled["morning_enabled"].get()
        state = "normal" if enabled else "disabled"

        # 更新输入框状态
        self.morning_start_entry.config(state=state)
        self.morning_end_entry.config(state=state)

        # 更新按钮状态
        self.morning_start_minus_btn.config(state=state)
        self.morning_start_plus_btn.config(state=state)
        self.morning_end_minus_btn.config(state=state)
        self.morning_end_plus_btn.config(state=state)

        # 触发参数变化事件
        self.on_param_change()

    def on_afternoon_enabled_change(self):
        """下午时段启用状态变化回调"""
        enabled = self.time_slot_enabled["afternoon_enabled"].get()
        state = "normal" if enabled else "disabled"

        # 更新输入框状态
        self.afternoon_start_entry.config(state=state)
        self.afternoon_end_entry.config(state=state)

        # 更新按钮状态
        self.afternoon_start_minus_btn.config(state=state)
        self.afternoon_start_plus_btn.config(state=state)
        self.afternoon_end_minus_btn.config(state=state)
        self.afternoon_end_plus_btn.config(state=state)

        # 触发参数变化事件
        self.on_param_change()

    def validate_and_apply_params(self):
        """验证并应用参数"""
        try:
            # 验证数值参数
            interval_min = int(self.runtime_params["interval_min"].get())
            interval_max = int(self.runtime_params["interval_max"].get())
            daily_limit = int(self.runtime_params["daily_limit"].get())
            max_per_window = int(self.runtime_params["max_per_window"].get())
            rest_trigger = int(self.runtime_params["rest_trigger"].get())
            rest_duration = int(self.runtime_params["rest_duration"].get())

            # 验证时间格式
            self.validate_time_format(self.runtime_params["morning_start"].get())
            self.validate_time_format(self.runtime_params["morning_end"].get())
            self.validate_time_format(self.runtime_params["afternoon_start"].get())
            self.validate_time_format(self.runtime_params["afternoon_end"].get())

            # 验证逻辑关系
            if interval_min >= interval_max:
                raise ValueError("最小间隔必须小于最大间隔")
            if daily_limit <= 0:
                raise ValueError("每日添加上限必须大于0")
            if max_per_window < 0:
                raise ValueError("每窗口最大添加数量不能为负数")
            if rest_trigger <= 0:
                raise ValueError("休息触发条件必须大于0")
            if rest_duration <= 0:
                raise ValueError("休息时长必须大于0")

            # 如果有控制器，立即应用参数
            if self.controller:
                self.apply_params_to_controller()

            return True

        except ValueError as e:
            raise ValueError(f"参数验证失败: {e}")

    def validate_time_format(self, time_str):
        """验证时间格式"""
        try:
            parts = time_str.split(':')
            if len(parts) != 2:
                raise ValueError(f"时间格式错误: {time_str}")

            hour = int(parts[0])
            minute = int(parts[1])

            if not (0 <= hour <= 23):
                raise ValueError(f"小时必须在0-23之间: {hour}")
            if not (0 <= minute <= 59):
                raise ValueError(f"分钟必须在0-59之间: {minute}")

        except ValueError:
            raise ValueError(f"时间格式错误: {time_str}，正确格式为 HH:MM")

    def apply_params_to_controller(self):
        """将参数应用到控制器 - 增强版，确保时间段配置正确传递"""
        if not self.controller:
            return

        try:
            # 更新控制器的配置
            config_updates = {
                "runtime_parameters": {
                    "single_add_interval": {
                        "min": int(self.runtime_params["interval_min"].get()),
                        "max": int(self.runtime_params["interval_max"].get())
                    },
                    "daily_add_limit": {
                        "value": int(self.runtime_params["daily_limit"].get())
                    },
                    "max_adds_per_window": {
                        "value": int(self.runtime_params["max_per_window"].get())
                    },
                    "execution_time_slots": {
                        "morning": {
                            "start": self.runtime_params["morning_start"].get(),
                            "end": self.runtime_params["morning_end"].get(),
                            "enabled": self.time_slot_enabled["morning_enabled"].get()
                        },
                        "afternoon": {
                            "start": self.runtime_params["afternoon_start"].get(),
                            "end": self.runtime_params["afternoon_end"].get(),
                            "enabled": self.time_slot_enabled["afternoon_enabled"].get()
                        }
                    }
                },
                "auto_rest_config": {
                    "rest_trigger": {
                        "friends_count": int(self.runtime_params["rest_trigger"].get())
                    },
                    "rest_duration": {
                        "minutes": int(self.runtime_params["rest_duration"].get())
                    }
                }
            }

            # 🔧 修复：更新控制器配置，确保时间段设置生效
            if hasattr(self.controller, 'config_manager'):
                for key, value in config_updates.items():
                    self.controller.config_manager.set(key, value)

                # 🔧 重要：同步更新控制器的config属性
                if hasattr(self.controller, 'config'):
                    self.controller.config = self.controller.config_manager.config

            # 🔧 记录时间段配置应用情况
            time_slots = config_updates["runtime_parameters"]["execution_time_slots"]
            self.log_message("INFO", f"时间段配置已更新:")
            self.log_message("INFO", f"  上午: {time_slots['morning']['start']}-{time_slots['morning']['end']} (启用: {time_slots['morning']['enabled']})")
            self.log_message("INFO", f"  下午: {time_slots['afternoon']['start']}-{time_slots['afternoon']['end']} (启用: {time_slots['afternoon']['enabled']})")
            self.log_message("INFO", "运行参数已实时更新到控制器")

        except Exception as e:
            self.log_message("ERROR", f"应用参数到控制器失败: {e}")

    def validate_runtime_params(self):
        """验证运行参数"""
        try:
            self.validate_and_apply_params()
            messagebox.showinfo("验证成功", "所有运行参数验证通过！")
            self.log_message("INFO", "运行参数验证成功")
        except Exception as e:
            messagebox.showerror("验证失败", str(e))
            self.log_message("ERROR", f"运行参数验证失败: {e}")

    def save_runtime_params(self):
        """保存运行参数到配置文件"""
        try:
            # 先验证参数
            self.validate_and_apply_params()

            # 读取现有配置
            with open("config.json", "r", encoding="utf-8") as f:
                config = json.load(f)

            # 更新配置
            config["runtime_parameters"] = {
                "single_add_interval": {
                    "min": int(self.runtime_params["interval_min"].get()),
                    "max": int(self.runtime_params["interval_max"].get()),
                    "unit": "seconds",
                    "description": "单次添加间隔范围"
                },
                "daily_add_limit": {
                    "value": int(self.runtime_params["daily_limit"].get()),
                    "description": "每日添加上限"
                },
                "max_adds_per_window": {
                    "value": int(self.runtime_params["max_per_window"].get()),
                    "description": "每窗口最大添加数量，0表示不限制"
                },
                "execution_time_slots": {
                    "morning": {
                        "start": self.runtime_params["morning_start"].get(),
                        "end": self.runtime_params["morning_end"].get(),
                        "enabled": self.time_slot_enabled["morning_enabled"].get(),
                        "description": "上午执行时段"
                    },
                    "afternoon": {
                        "start": self.runtime_params["afternoon_start"].get(),
                        "end": self.runtime_params["afternoon_end"].get(),
                        "enabled": self.time_slot_enabled["afternoon_enabled"].get(),
                        "description": "下午执行时段"
                    }
                }
            }

            config["auto_rest_config"] = {
                "rest_trigger": {
                    "friends_count": int(self.runtime_params["rest_trigger"].get()),
                    "description": "每添加X个好友后休息"
                },
                "rest_duration": {
                    "minutes": int(self.runtime_params["rest_duration"].get()),
                    "description": "休息时长（分钟）"
                },
                "enabled": True
            }

            # 保存配置
            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("保存成功", "运行参数已保存到配置文件！")
            self.log_message("INFO", "运行参数已保存到配置文件")

        except Exception as e:
            messagebox.showerror("保存失败", f"保存运行参数失败: {e}")
            self.log_message("ERROR", f"保存运行参数失败: {e}")

    def reset_runtime_params(self):
        """重置运行参数为默认值"""
        if messagebox.askyesno("确认重置", "确定要重置所有运行参数为默认值吗？"):
            try:
                # 重置为默认值
                self.runtime_params["interval_min"].set("50")
                self.runtime_params["interval_max"].set("60")
                self.runtime_params["daily_limit"].set("200")
                self.runtime_params["max_per_window"].set("20")
                self.runtime_params["morning_start"].set("08:00")
                self.runtime_params["morning_end"].set("12:00")
                self.runtime_params["afternoon_start"].set("14:00")
                self.runtime_params["afternoon_end"].set("23:59")
                self.runtime_params["rest_trigger"].set("20")
                self.runtime_params["rest_duration"].set("5")

                # 应用参数
                self.validate_and_apply_params()

                messagebox.showinfo("重置成功", "运行参数已重置为默认值！")
                self.log_message("INFO", "运行参数已重置为默认值")

            except Exception as e:
                messagebox.showerror("重置失败", f"重置运行参数失败: {e}")
                self.log_message("ERROR", f"重置运行参数失败: {e}")

    # ==================== 状态显示更新方法 ====================

    def update_status_display(self):
        """🔧 更新状态显示 - 增强实时更新版"""
        try:
            # 更新总进度
            total_contacts = self.execution_stats.get("total_contacts", 0)
            processed_contacts = self.execution_stats.get("processed_contacts", 0)

            if total_contacts > 0:
                progress_percent = (processed_contacts / total_contacts) * 100
                self.status_vars["total_progress"].set(f"{processed_contacts}/{total_contacts}")
                # 🔧 修复：直接更新主进度条，移除动画效果
                self.main_progress_bar.config(value=progress_percent)
                self.main_progress_percent.config(text=f"{progress_percent:.1f}%")
                self.log_message("DEBUG", f"📊 主进度条更新: {progress_percent:.1f}% ({processed_contacts}/{total_contacts})")
            else:
                self.status_vars["total_progress"].set("0/0")
                self.main_progress_bar.config(value=0)
                self.main_progress_percent.config(text="0%")

            # 更新详细统计
            self.status_vars["planned_count"].set(str(total_contacts))
            self.status_vars["current_progress"].set(str(processed_contacts))
            self.status_vars["success_count"].set(str(self.execution_stats.get("successful_adds", 0)))
            # 🔧 保留error_count更新逻辑（用于内部处理），但不在界面显示
            self.status_vars["error_count"].set(str(self.execution_stats.get("failed_adds", 0)))

            # 移除冗余的窗口信息更新（详情统计中已有当前微信信息）

            # 🔧 实时窗口状态检测（仅在运行时检测）
            if self.is_running and self.controller:
                try:
                    current_windows = self.controller.get_wechat_windows()
                    window_count = len(current_windows) if current_windows else 0
                    self.update_window_detection(f"✅ 已检测到{window_count}个窗口" if window_count > 0 else "❌ 未找到窗口")
                except Exception as e:
                    self.log_message("DEBUG", f"实时窗口检测失败: {e}")
                    self.update_window_detection("❓ 窗口状态未知")

            # 更新倒计时（这里可以根据实际需要实现）
            self.update_countdown()

            # 🔧 移除单独的GUI刷新，由统一更新方法处理

        except Exception as e:
            self.log_message("ERROR", f"更新状态显示失败: {e}")
        finally:
            # 🔧 优化：进一步降低更新频率，减少界面闪烁
            # 执行期间每5秒更新一次，非执行期间每15秒更新一次
            update_interval = 5000 if self.is_running else 15000
            self.root.after(update_interval, self.update_status_display)

    def update_countdown_display(self, countdown_text: str):
        """🔧 更新倒计时显示（由控制器回调）"""
        try:
            self.status_vars["countdown"].set(countdown_text)
        except Exception as e:
            self.log_message("DEBUG", f"倒计时显示更新失败: {e}")

    def update_countdown(self):
        """更新操作倒计时"""
        try:
            # 🔧 修复：如果没有来自控制器的倒计时更新，则显示默认倒计时
            if self.is_running:
                # 检查是否有来自控制器的倒计时更新
                current_countdown = self.status_vars["countdown"].get()
                if not current_countdown or current_countdown == "N/A":
                    # 获取当前配置的间隔时间
                    interval_min = int(self.runtime_params["interval_min"].get())
                    interval_max = int(self.runtime_params["interval_max"].get())
                    avg_interval = (interval_min + interval_max) / 2

                    # 显示平均间隔时间作为默认倒计时
                    import random
                    countdown = random.randint(1, int(avg_interval))
                    self.status_vars["countdown"].set(f"{countdown}秒")
            else:
                self.status_vars["countdown"].set("0秒")

        except Exception:
            self.status_vars["countdown"].set("N/A")

    def load_runtime_params_from_config(self):
        """从配置文件加载运行参数"""
        try:
            # 🔧 增强的配置文件读取，支持多种编码和错误处理
            config = None
            encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'latin1']

            for encoding in encodings:
                try:
                    with open("config.json", "r", encoding=encoding) as f:
                        content = f.read().strip()
                        if not content:
                            self.log_message("WARNING", "配置文件为空，使用默认运行参数")
                            return
                        config = json.loads(content)
                        break
                except UnicodeDecodeError:
                    continue
                except json.JSONDecodeError as e:
                    self.log_message("WARNING", f"配置文件JSON格式错误: {e}，使用默认运行参数")
                    return
                except Exception as e:
                    if encoding == encodings[-1]:
                        raise e
                    continue

            if config is None:
                self.log_message("WARNING", "无法读取配置文件，使用默认运行参数")
                return

            # 加载运行参数
            runtime_params = config.get("runtime_parameters", {})
            if runtime_params:
                interval_config = runtime_params.get("single_add_interval", {})
                self.runtime_params["interval_min"].set(str(interval_config.get("min", 50)))
                self.runtime_params["interval_max"].set(str(interval_config.get("max", 60)))

                daily_limit = runtime_params.get("daily_add_limit", {})
                self.runtime_params["daily_limit"].set(str(daily_limit.get("value", 200)))

                max_per_window = runtime_params.get("max_adds_per_window", {})
                self.runtime_params["max_per_window"].set(str(max_per_window.get("value", 20)))

                time_slots = runtime_params.get("execution_time_slots", {})
                morning = time_slots.get("morning", {})
                afternoon = time_slots.get("afternoon", {})

                self.runtime_params["morning_start"].set(morning.get("start", "08:00"))
                self.runtime_params["morning_end"].set(morning.get("end", "12:00"))
                self.runtime_params["afternoon_start"].set(afternoon.get("start", "14:00"))
                self.runtime_params["afternoon_end"].set(afternoon.get("end", "23:59"))

                # 加载时段启用状态
                self.time_slot_enabled["morning_enabled"].set(morning.get("enabled", True))
                self.time_slot_enabled["afternoon_enabled"].set(afternoon.get("enabled", True))

                # 更新输入框状态
                self.on_morning_enabled_change()
                self.on_afternoon_enabled_change()

            # 加载自动休息配置
            rest_config = config.get("auto_rest_config", {})
            if rest_config:
                rest_trigger = rest_config.get("rest_trigger", {})
                self.runtime_params["rest_trigger"].set(str(rest_trigger.get("friends_count", 20)))

                rest_duration = rest_config.get("rest_duration", {})
                self.runtime_params["rest_duration"].set(str(rest_duration.get("minutes", 5)))

            self.log_message("INFO", "运行参数已从配置文件加载")

        except Exception as e:
            self.log_message("WARNING", f"加载运行参数失败，使用默认值: {e}")

    def adjust_numeric_value(self, var, delta, min_val, max_val):
        """调整数值变量的值"""
        try:
            current_val = int(var.get())
            new_val = max(min_val, min(max_val, current_val + delta))
            var.set(str(new_val))
            self.on_param_change()
        except ValueError:
            # 如果当前值不是有效数字，设置为最小值
            var.set(str(min_val))
            self.on_param_change()

    def adjust_float_value(self, var, delta, min_val, max_val):
        """调整浮点数变量的值"""
        try:
            current_val = float(var.get())
            new_val = max(min_val, min(max_val, current_val + delta))
            var.set(f"{new_val:.1f}")
            self.on_param_change()
        except ValueError:
            # 如果当前值不是有效数字，设置为最小值
            var.set(f"{min_val:.1f}")
            self.on_param_change()

    def adjust_time_value(self, var, delta_minutes):
        """调整时间变量的值（以分钟为单位）"""
        try:
            current_time = var.get()
            # 解析当前时间
            if ':' in current_time:
                hours, minutes = map(int, current_time.split(':'))
            else:
                # 如果格式不正确，设置为默认时间
                hours, minutes = 10, 0

            # 计算总分钟数
            total_minutes = hours * 60 + minutes + delta_minutes

            # 确保时间在有效范围内（00:00-23:59）
            total_minutes = max(0, min(1439, total_minutes))  # 1439 = 23*60 + 59

            # 转换回小时和分钟
            new_hours = total_minutes // 60
            new_minutes = total_minutes % 60

            # 格式化时间字符串
            new_time = f"{new_hours:02d}:{new_minutes:02d}"
            var.set(new_time)
            self.on_param_change()

        except (ValueError, AttributeError):
            # 如果当前值不是有效时间格式，设置为默认时间
            var.set("08:00")
            self.on_param_change()

    def run(self):
        """运行GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


class GUILogHandler(logging.Handler):
    """自定义日志处理器，将日志发送到GUI"""

    def __init__(self, message_queue):
        super().__init__()
        self.message_queue = message_queue

    def emit(self, record):
        try:
            msg = self.format(record)
            self.message_queue.put(('log', record.levelname, msg))
        except Exception:
            self.handleError(record)


def main():
    """主程序入口"""
    try:
        # 创建并运行GUI
        app = WeChatAutomationGUI()
        app.run()
    except Exception as e:
        print(f"启动GUI失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
