{"analysis_time": "2025-08-02T15:24:59.795897", "total_stats": {"total_files": 41, "total_lines": 70, "total_duplicates": 4, "files_with_duplicates": 1, "overall_duplicate_ratio": 5.714285714285714}, "file_results": {"main_controller_20250801_234601.log": {"file_name": "main_controller_20250801_234601.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250801_234738.log": {"file_name": "main_controller_20250801_234738.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250801_234959.log": {"file_name": "main_controller_20250801_234959.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_001040.log": {"file_name": "main_controller_20250802_001040.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_002751.log": {"file_name": "main_controller_20250802_002751.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_010321.log": {"file_name": "main_controller_20250802_010321.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_011242.log": {"file_name": "main_controller_20250802_011242.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_015449.log": {"file_name": "main_controller_20250802_015449.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_020850.log": {"file_name": "main_controller_20250802_020850.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_020904.log": {"file_name": "main_controller_20250802_020904.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_020940.log": {"file_name": "main_controller_20250802_020940.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_022417.log": {"file_name": "main_controller_20250802_022417.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_023440.log": {"file_name": "main_controller_20250802_023440.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_024317.log": {"file_name": "main_controller_20250802_024317.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_024448.log": {"file_name": "main_controller_20250802_024448.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_025143.log": {"file_name": "main_controller_20250802_025143.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_025914.log": {"file_name": "main_controller_20250802_025914.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_025923.log": {"file_name": "main_controller_20250802_025923.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_025931.log": {"file_name": "main_controller_20250802_025931.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_030017.log": {"file_name": "main_controller_20250802_030017.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_030039.log": {"file_name": "main_controller_20250802_030039.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_031618.log": {"file_name": "main_controller_20250802_031618.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_031710.log": {"file_name": "main_controller_20250802_031710.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_032305.log": {"file_name": "main_controller_20250802_032305.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_032932.log": {"file_name": "main_controller_20250802_032932.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_033101.log": {"file_name": "main_controller_20250802_033101.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_045343.log": {"file_name": "main_controller_20250802_045343.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_050333.log": {"file_name": "main_controller_20250802_050333.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_064617.log": {"file_name": "main_controller_20250802_064617.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_113355.log": {"file_name": "main_controller_20250802_113355.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_113543.log": {"file_name": "main_controller_20250802_113543.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_114014.log": {"file_name": "main_controller_20250802_114014.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_130123.log": {"file_name": "main_controller_20250802_130123.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_131348.log": {"file_name": "main_controller_20250802_131348.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_132931.log": {"file_name": "main_controller_20250802_132931.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_133156.log": {"file_name": "main_controller_20250802_133156.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_135603.log": {"file_name": "main_controller_20250802_135603.log", "total_lines": 70, "parsed_logs": 70, "unique_messages": 66, "total_duplicates": 4, "duplicate_ratio": 5.714285714285714, "most_duplicated": [["✅ 频率错误处理器初始化完成", 2], ["✅ 配置文件加载成功: config.json", 2], ["🌅 上午时段: 08:00 - 12:00 (启用: True)", 2], ["🌇 下午时段: 14:00 - 23:59 (启用: True)", 2], ["🚀 开始初始化主控制器...", 1], ["📋 正在初始化配置管理器...", 1], ["✅ 配置管理器初始化完成", 1], ["📊 正在初始化数据管理器...", 1], ["✅ 数据管理器初始化完成", 1], ["⏱️ 正在初始化频率错误处理器...", 1]], "level_distribution": {"INFO": 69, "WARNING": 1}, "duplicate_groups": {"🚀 开始初始化主控制器...": [{"timestamp": "2025-08-02 13:56:03,045", "module": "main_controller", "level": "INFO", "message": "🚀 开始初始化主控制器...", "core": "🚀 开始初始化主控制器..."}], "📋 正在初始化配置管理器...": [{"timestamp": "2025-08-02 13:56:03,046", "module": "main_controller", "level": "INFO", "message": "📋 正在初始化配置管理器...", "core": "📋 正在初始化配置管理器..."}], "✅ 配置管理器初始化完成": [{"timestamp": "2025-08-02 13:56:03,048", "module": "main_controller", "level": "INFO", "message": "✅ 配置管理器初始化完成", "core": "✅ 配置管理器初始化完成"}], "📊 正在初始化数据管理器...": [{"timestamp": "2025-08-02 13:56:03,049", "module": "main_controller", "level": "INFO", "message": "📊 正在初始化数据管理器...", "core": "📊 正在初始化数据管理器..."}], "✅ 数据管理器初始化完成": [{"timestamp": "2025-08-02 13:56:03,050", "module": "main_controller", "level": "INFO", "message": "✅ 数据管理器初始化完成", "core": "✅ 数据管理器初始化完成"}], "⏱️ 正在初始化频率错误处理器...": [{"timestamp": "2025-08-02 13:56:03,050", "module": "main_controller", "level": "INFO", "message": "⏱️ 正在初始化频率错误处理器...", "core": "⏱️ 正在初始化频率错误处理器..."}], "✅ 鼠标基础操作已启用（视觉反馈已禁用）": [{"timestamp": "2025-08-02 13:56:03,051", "module": "modules.frequency_error_handler.FrequencyErrorHandler", "level": "INFO", "message": "✅ 鼠标基础操作已启用（视觉反馈已禁用）", "core": "✅ 鼠标基础操作已启用（视觉反馈已禁用）"}], "✅ 频率错误处理器初始化完成": [{"timestamp": "2025-08-02 13:56:03,056", "module": "modules.frequency_error_handler.FrequencyErrorHandler", "level": "INFO", "message": "✅ 频率错误处理器初始化完成", "core": "✅ 频率错误处理器初始化完成"}, {"timestamp": "2025-08-02 13:56:03,064", "module": "main_controller", "level": "INFO", "message": "✅ 频率错误处理器初始化完成", "core": "✅ 频率错误处理器初始化完成"}], "📋 当前黑名单中有 0 个窗口": [{"timestamp": "2025-08-02 13:56:03,057", "module": "modules.frequency_error_handler.FrequencyErrorHandler", "level": "INFO", "message": "📋 当前黑名单中有 0 个窗口", "core": "📋 当前黑名单中有 0 个窗口"}], "🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口": [{"timestamp": "2025-08-02 13:56:03,059", "module": "modules.frequency_error_handler.FrequencyErrorHandler", "level": "WARNING", "message": "🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口", "core": "🚫 过滤异常小窗口: 微信 (160x28) - 可能是残留窗口"}], "📊 有效微信窗口统计: 1 个": [{"timestamp": "2025-08-02 13:56:03,061", "module": "modules.frequency_error_handler.FrequencyErrorHandler", "level": "INFO", "message": "📊 有效微信窗口统计: 1 个", "core": "📊 有效微信窗口统计: 1 个"}], "🖥️ 检测到系统中共有 1 个微信窗口": [{"timestamp": "2025-08-02 13:56:03,062", "module": "modules.frequency_error_handler.FrequencyErrorHandler", "level": "INFO", "message": "🖥️ 检测到系统中共有 1 个微信窗口", "core": "🖥️ 检测到系统中共有 1 个微信窗口"}], "🪟 正在初始化窗口管理器...": [{"timestamp": "2025-08-02 13:56:03,064", "module": "main_controller", "level": "INFO", "message": "🪟 正在初始化窗口管理器...", "core": "🪟 正在初始化窗口管理器..."}], "✅ 配置文件加载成功: config.json": [{"timestamp": "2025-08-02 13:56:03,065", "module": "modules.window_manager", "level": "INFO", "message": "✅ 配置文件加载成功: config.json", "core": "✅ 配置文件加载成功: config.json"}, {"timestamp": "2025-08-02 13:56:03,067", "module": "modules.main_interface", "level": "INFO", "message": "✅ 配置文件加载成功: config.json", "core": "✅ 配置文件加载成功: config.json"}], "✅ 窗口管理器初始化完成": [{"timestamp": "2025-08-02 13:56:03,065", "module": "main_controller", "level": "INFO", "message": "✅ 窗口管理器初始化完成", "core": "✅ 窗口管理器初始化完成"}], "🖥️ 正在初始化主界面管理器...": [{"timestamp": "2025-08-02 13:56:03,066", "module": "main_controller", "level": "INFO", "message": "🖥️ 正在初始化主界面管理器...", "core": "🖥️ 正在初始化主界面管理器..."}], "✅ 微信主界面操作模块初始化完成": [{"timestamp": "2025-08-02 13:56:03,073", "module": "modules.main_interface", "level": "INFO", "message": "✅ 微信主界面操作模块初始化完成", "core": "✅ 微信主界面操作模块初始化完成"}], "✅ 主界面管理器初始化完成": [{"timestamp": "2025-08-02 13:56:03,074", "module": "main_controller", "level": "INFO", "message": "✅ 主界面管理器初始化完成", "core": "✅ 主界面管理器初始化完成"}], "👥 正在初始化自动添加好友模块...": [{"timestamp": "2025-08-02 13:56:03,076", "module": "main_controller", "level": "INFO", "message": "👥 正在初始化自动添加好友模块...", "core": "👥 正在初始化自动添加好友模块..."}], "微信自动添加朋友脚本初始化完成": [{"timestamp": "2025-08-02 13:56:03,078", "module": "WeChatAutoAdd", "level": "INFO", "message": "微信自动添加朋友脚本初始化完成", "core": "微信自动添加朋友脚本初始化完成"}], "✅ 自动添加好友模块初始化完成": [{"timestamp": "2025-08-02 13:56:03,085", "module": "main_controller", "level": "INFO", "message": "✅ 自动添加好友模块初始化完成", "core": "✅ 自动添加好友模块初始化完成"}], "📝 正在初始化好友申请处理器...": [{"timestamp": "2025-08-02 13:56:03,087", "module": "main_controller", "level": "INFO", "message": "📝 正在初始化好友申请处理器...", "core": "📝 正在初始化好友申请处理器..."}], "✅ 已加载配置文件: config.json": [{"timestamp": "2025-08-02 13:56:03,088", "module": "modules.friend_request_window", "level": "INFO", "message": "✅ 已加载配置文件: config.json", "core": "✅ 已加载配置文件: config.json"}], "🔧 使用固定坐标配置:": [{"timestamp": "2025-08-02 13:56:03,088", "module": "modules.friend_request_window", "level": "INFO", "message": "🔧 使用固定坐标配置:", "core": "🔧 使用固定坐标配置:"}], "📍 验证信息输入框: (960, 330)": [{"timestamp": "2025-08-02 13:56:03,088", "module": "modules.friend_request_window", "level": "INFO", "message": "   📍 验证信息输入框: (960, 330)", "core": "📍 验证信息输入框: (960, 330)"}], "📍 备注信息输入框: (960, 450)": [{"timestamp": "2025-08-02 13:56:03,089", "module": "modules.friend_request_window", "level": "INFO", "message": "   📍 备注信息输入框: (960, 450)", "core": "📍 备注信息输入框: (960, 450)"}], "📍 确定按钮: (910, 840)": [{"timestamp": "2025-08-02 13:56:03,089", "module": "modules.friend_request_window", "level": "INFO", "message": "   📍 确定按钮: (910, 840)", "core": "📍 确定按钮: (910, 840)"}], "✅ 微信好友申请窗口处理器初始化完成": [{"timestamp": "2025-08-02 13:56:03,090", "module": "modules.friend_request_window", "level": "INFO", "message": "✅ 微信好友申请窗口处理器初始化完成", "core": "✅ 微信好友申请窗口处理器初始化完成"}], "✅ 好友申请处理器初始化完成": [{"timestamp": "2025-08-02 13:56:03,090", "module": "main_controller", "level": "INFO", "message": "✅ 好友申请处理器初始化完成", "core": "✅ 好友申请处理器初始化完成"}], "🔗 正在建立组件间引用关系...": [{"timestamp": "2025-08-02 13:56:03,092", "module": "main_controller", "level": "INFO", "message": "🔗 正在建立组件间引用关系...", "core": "🔗 正在建立组件间引用关系..."}], "✅ 已设置频率错误处理器引用": [{"timestamp": "2025-08-02 13:56:03,093", "module": "modules.window_manager", "level": "INFO", "message": "✅ 已设置频率错误处理器引用", "core": "✅ 已设置频率错误处理器引用"}], "✅ 组件间引用关系建立完成": [{"timestamp": "2025-08-02 13:56:03,093", "module": "main_controller", "level": "INFO", "message": "✅ 组件间引用关系建立完成", "core": "✅ 组件间引用关系建立完成"}], "🧠 正在启用智能检测功能...": [{"timestamp": "2025-08-02 13:56:03,093", "module": "main_controller", "level": "INFO", "message": "🧠 正在启用智能检测功能...", "core": "🧠 正在启用智能检测功能..."}], "🧠 启用智能检测功能...": [{"timestamp": "2025-08-02 13:56:03,094", "module": "main_controller", "level": "INFO", "message": "🧠 启用智能检测功能...", "core": "🧠 启用智能检测功能..."}], "✅ 智能检测功能已启用": [{"timestamp": "2025-08-02 13:56:03,094", "module": "main_controller", "level": "INFO", "message": "✅ 智能检测功能已启用", "core": "✅ 智能检测功能已启用"}], "🔍 支持的检测方法:": [{"timestamp": "2025-08-02 13:56:03,094", "module": "main_controller", "level": "INFO", "message": "🔍 支持的检测方法:", "core": "🔍 支持的检测方法:"}], "1. 通过子控件检测确定按钮": [{"timestamp": "2025-08-02 13:56:03,094", "module": "main_controller", "level": "INFO", "message": "   1. 通过子控件检测确定按钮", "core": "1. 通过子控件检测确定按钮"}], "2. 通过文本识别检测确定按钮": [{"timestamp": "2025-08-02 13:56:03,094", "module": "main_controller", "level": "INFO", "message": "   2. 通过文本识别检测确定按钮", "core": "2. 通过文本识别检测确定按钮"}], "3. 通过图像识别检测确定按钮": [{"timestamp": "2025-08-02 13:56:03,095", "module": "main_controller", "level": "INFO", "message": "   3. 通过图像识别检测确定按钮", "core": "3. 通过图像识别检测确定按钮"}], "4. 基于相对位置计算确定按钮坐标": [{"timestamp": "2025-08-02 13:56:03,095", "module": "main_controller", "level": "INFO", "message": "   4. 基于相对位置计算确定按钮坐标", "core": "4. 基于相对位置计算确定按钮坐标"}], "🖱️ 支持的点击方法:": [{"timestamp": "2025-08-02 13:56:03,095", "module": "main_controller", "level": "INFO", "message": "🖱️ 支持的点击方法:", "core": "🖱️ 支持的点击方法:"}], "1. 使用Win32 API点击": [{"timestamp": "2025-08-02 13:56:03,095", "module": "main_controller", "level": "INFO", "message": "   1. 使用Win32 API点击", "core": "1. 使用Win32 API点击"}], "2. 使用PyAutoGUI点击": [{"timestamp": "2025-08-02 13:56:03,098", "module": "main_controller", "level": "INFO", "message": "   2. 使用PyAutoGUI点击", "core": "2. 使用PyAutoGUI点击"}], "3. 使用键盘Enter键": [{"timestamp": "2025-08-02 13:56:03,099", "module": "main_controller", "level": "INFO", "message": "   3. 使用键盘Enter键", "core": "3. 使用键盘Enter键"}], "4. 发送窗口消息": [{"timestamp": "2025-08-02 13:56:03,100", "module": "main_controller", "level": "INFO", "message": "   4. 发送窗口消息", "core": "4. 发送窗口消息"}], "🔄 重试设置: 检测最多3次, 点击最多4次": [{"timestamp": "2025-08-02 13:56:03,101", "module": "main_controller", "level": "INFO", "message": "🔄 重试设置: 检测最多3次, 点击最多4次", "core": "🔄 重试设置: 检测最多3次, 点击最多4次"}], "📱 已启用跨分辨率兼容性": [{"timestamp": "2025-08-02 13:56:03,101", "module": "main_controller", "level": "INFO", "message": "📱 已启用跨分辨率兼容性", "core": "📱 已启用跨分辨率兼容性"}], "🪟 已启用窗口位置无关性": [{"timestamp": "2025-08-02 13:56:03,101", "module": "main_controller", "level": "INFO", "message": "🪟 已启用窗口位置无关性", "core": "🪟 已启用窗口位置无关性"}], "✅ 智能检测功能启用完成": [{"timestamp": "2025-08-02 13:56:03,102", "module": "main_controller", "level": "INFO", "message": "✅ 智能检测功能启用完成", "core": "✅ 智能检测功能启用完成"}], "⚙️ 正在初始化执行状态...": [{"timestamp": "2025-08-02 13:56:03,102", "module": "main_controller", "level": "INFO", "message": "⚙️ 正在初始化执行状态...", "core": "⚙️ 正在初始化执行状态..."}], "✅ 执行状态初始化完成": [{"timestamp": "2025-08-02 13:56:03,102", "module": "main_controller", "level": "INFO", "message": "✅ 执行状态初始化完成", "core": "✅ 执行状态初始化完成"}], "📞 正在初始化GUI回调函数...": [{"timestamp": "2025-08-02 13:56:03,103", "module": "main_controller", "level": "INFO", "message": "📞 正在初始化GUI回调函数...", "core": "📞 正在初始化GUI回调函数..."}], "✅ GUI回调函数初始化完成": [{"timestamp": "2025-08-02 13:56:03,103", "module": "main_controller", "level": "INFO", "message": "✅ GUI回调函数初始化完成", "core": "✅ GUI回调函数初始化完成"}], "📊 正在初始化执行统计...": [{"timestamp": "2025-08-02 13:56:03,103", "module": "main_controller", "level": "INFO", "message": "📊 正在初始化执行统计...", "core": "📊 正在初始化执行统计..."}], "✅ 执行统计初始化完成": [{"timestamp": "2025-08-02 13:56:03,104", "module": "main_controller", "level": "INFO", "message": "✅ 执行统计初始化完成", "core": "✅ 执行统计初始化完成"}], "🎉 主控制器初始化完全完成！": [{"timestamp": "2025-08-02 13:56:03,104", "module": "main_controller", "level": "INFO", "message": "🎉 主控制器初始化完全完成！", "core": "🎉 主控制器初始化完全完成！"}], "✅ 微信自动化主控制器初始化完成": [{"timestamp": "2025-08-02 13:56:03,105", "module": "main_controller", "level": "INFO", "message": "✅ 微信自动化主控制器初始化完成", "core": "✅ 微信自动化主控制器初始化完成"}], "📅 当前北京时间:": [{"timestamp": "2025-08-02 13:56:03,105", "module": "main_controller", "level": "INFO", "message": "📅 当前北京时间: 2025-08-02 21:56:03", "core": "📅 当前北京时间:"}], "⏰ 时间段配置:": [{"timestamp": "2025-08-02 13:56:03,106", "module": "main_controller", "level": "INFO", "message": "⏰ 时间段配置:", "core": "⏰ 时间段配置:"}], "🌅 上午时段: 08:00 - 12:00 (启用: True)": [{"timestamp": "2025-08-02 13:56:03,106", "module": "main_controller", "level": "INFO", "message": "   🌅 上午时段: 08:00 - 12:00 (启用: True)", "core": "🌅 上午时段: 08:00 - 12:00 (启用: True)"}, {"timestamp": "2025-08-02 13:56:03,107", "module": "main_controller", "level": "INFO", "message": "   🌅 上午时段: 08:00 - 12:00 (启用: True)", "core": "🌅 上午时段: 08:00 - 12:00 (启用: True)"}], "🌇 下午时段: 14:00 - 23:59 (启用: True)": [{"timestamp": "2025-08-02 13:56:03,106", "module": "main_controller", "level": "INFO", "message": "   🌇 下午时段: 14:00 - 23:59 (启用: True)", "core": "🌇 下午时段: 14:00 - 23:59 (启用: True)"}, {"timestamp": "2025-08-02 13:56:03,108", "module": "main_controller", "level": "INFO", "message": "   🌇 下午时段: 14:00 - 23:59 (启用: True)", "core": "🌇 下午时段: 14:00 - 23:59 (启用: True)"}], "🕐 正在检查时间权限 - 当前北京时间: 21:56": [{"timestamp": "2025-08-02 13:56:03,107", "module": "main_controller", "level": "INFO", "message": "🕐 正在检查时间权限 - 当前北京时间: 21:56", "core": "🕐 正在检查时间权限 - 当前北京时间: 21:56"}], "📋 时间段配置检查:": [{"timestamp": "2025-08-02 13:56:03,107", "module": "main_controller", "level": "INFO", "message": "📋 时间段配置检查:", "core": "📋 时间段配置检查:"}], "⏰ 当前时间 21:56 不在上午时段 08:00-12:00 内": [{"timestamp": "2025-08-02 13:56:03,108", "module": "main_controller", "level": "INFO", "message": "⏰ 当前时间 21:56 不在上午时段 08:00-12:00 内", "core": "⏰ 当前时间 21:56 不在上午时段 08:00-12:00 内"}], "✅ 时间验证通过 - 当前时间 21:56 在下午时段 14:00-23:59 内": [{"timestamp": "2025-08-02 13:56:03,108", "module": "main_controller", "level": "INFO", "message": "✅ 时间验证通过 - 当前时间 21:56 在下午时段 14:00-23:59 内", "core": "✅ 时间验证通过 - 当前时间 21:56 在下午时段 14:00-23:59 内"}], "✅ 当前时间在允许的执行时间段内": [{"timestamp": "2025-08-02 13:56:03,109", "module": "main_controller", "level": "INFO", "message": "✅ 当前时间在允许的执行时间段内", "core": "✅ 当前时间在允许的执行时间段内"}]}}, "main_controller_20250802_140358.log": {"file_name": "main_controller_20250802_140358.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_143644.log": {"file_name": "main_controller_20250802_143644.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_145055.log": {"file_name": "main_controller_20250802_145055.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}, "main_controller_20250802_150142.log": {"file_name": "main_controller_20250802_150142.log", "total_lines": 0, "parsed_logs": 0, "unique_messages": 0, "total_duplicates": 0, "duplicate_ratio": 0, "most_duplicated": [], "level_distribution": {}, "duplicate_groups": {}}}}