#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证表格修复效果
测试字段映射和数据显示是否正确
"""

import os
import sys
from pathlib import Path

# 添加modules路径
sys.path.append(str(Path(__file__).parent / "modules"))

def verify_table_fix():
    """验证表格修复效果"""
    
    excel_file = "添加好友名单.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return
    
    try:
        from modules.data_manager import DataManager
        
        print("🔧 验证表格数据映射修复效果")
        print("=" * 60)
        
        # 创建数据管理器
        data_manager = DataManager(excel_file)
        
        # 加载数据
        contacts_data = data_manager.load_phone_numbers()
        
        if not contacts_data:
            print("⚠️ Excel文件中无数据")
            return
        
        print(f"📊 总记录数: {len(contacts_data)}")
        print("=" * 60)
        
        # 模拟GUI的数据处理逻辑
        print("🔧 模拟GUI数据处理逻辑:")
        
        success_count = 0
        failed_count = 0
        pending_count = 0
        
        for i, contact in enumerate(contacts_data[:10], 1):
            phone = contact.get('phone', '')
            status = contact.get('status', '待处理')
            
            # 🔧 使用修复后的字段映射逻辑
            result = contact.get('result', '') or contact.get('message', '')
            update_time = contact.get('update_time', '') or contact.get('timestamp', '')
            
            # 🔧 优化数据显示格式
            if not result or result.strip() == '':
                result = '未处理'
            
            if not update_time or update_time.strip() == '':
                update_time = '未更新'
            elif isinstance(update_time, str) and len(update_time) > 19:
                update_time = update_time[:19]
            
            # 统计状态
            if status == '成功':
                success_count += 1
            elif status in ['失败', '错误']:
                failed_count += 1
            else:
                pending_count += 1
            
            # 显示处理后的数据
            print(f"\n📱 记录 {i} (处理后):")
            print(f"  序号: {i}")
            print(f"  手机号码: {phone}")
            print(f"  状态: {status}")
            print(f"  结果: '{result}'")
            print(f"  最后更新时间: '{update_time}'")
        
        print("\n" + "=" * 60)
        print("📈 统计结果:")
        print(f"  总计: {len(contacts_data)}")
        print(f"  成功: {success_count}")
        print(f"  失败: {failed_count}")
        print(f"  待处理: {pending_count}")
        
        print("\n" + "=" * 60)
        print("✅ 验证结果:")
        
        # 检查修复效果
        has_result_data = False
        has_time_data = False
        
        for contact in contacts_data[:100]:  # 检查前100条
            result = contact.get('result', '') or contact.get('message', '')
            update_time = contact.get('update_time', '') or contact.get('timestamp', '')
            
            if result and result.strip():
                has_result_data = True
            if update_time and update_time.strip():
                has_time_data = True
        
        if has_result_data:
            print("✅ '结果'列数据映射成功 - 数据来源: message字段")
        else:
            print("❌ '结果'列数据映射失败 - 无有效数据")
        
        if has_time_data:
            print("✅ '最后更新时间'列数据映射成功 - 数据来源: timestamp字段")
        else:
            print("❌ '最后更新时间'列数据映射失败 - 无有效数据")
        
        print("\n🎯 修复总结:")
        print("1. 字段映射: Excel的'message'字段 → GUI的'结果'列")
        print("2. 字段映射: Excel的'timestamp'字段 → GUI的'最后更新时间'列")
        print("3. 列宽优化: 增加'结果'和'最后更新时间'列的显示宽度")
        print("4. 自动调整: 支持窗口大小变化时自动调整列宽")
        
        print("\n✅ 表格显示问题修复完成！")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_table_fix()
