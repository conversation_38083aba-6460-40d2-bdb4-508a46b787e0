#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志重复分析工具
分析现有日志文件中的重复日志问题，统计重复次数和模式
"""

import re
import sys
from pathlib import Path
from collections import Counter, defaultdict
from datetime import datetime
import json

def extract_log_core(message: str) -> str:
    """提取日志消息的核心内容，去除变量部分"""
    try:
        # 去除时间戳
        core = re.sub(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', '', message)
        
        # 去除模块名和日志级别
        core = re.sub(r' - \w+ - (ERROR|WARNING|INFO|DEBUG) - ', ' - ', core)
        
        # 去除数字变量
        core = re.sub(r'第 \d+ 个', '第 X 个', core)
        core = re.sub(r'\d+/\d+', 'X/Y', core)
        core = re.sub(r'句柄: \d+', '句柄: X', core)
        core = re.sub(r'窗口 \d+', '窗口 X', core)
        core = re.sub(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}', 'TIMESTAMP', core)
        
        # 去除多余空格
        core = ' '.join(core.split())
        
        return core.strip()
        
    except Exception:
        return message

def parse_log_line(line: str) -> dict:
    """解析日志行，提取时间戳、级别、模块和消息"""
    try:
        # 匹配日志格式: [时间] 级别: 消息
        pattern = r'\[(\d{2}:\d{2}:\d{2})\] (\w+): (.+)'
        match = re.match(pattern, line.strip())
        
        if match:
            time_str, level, message = match.groups()
            return {
                'time': time_str,
                'level': level,
                'message': message,
                'core': extract_log_core(message)
            }
        
        # 匹配标准日志格式: 时间戳 - 模块 - 级别 - 消息
        pattern2 = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - (.+?) - (\w+) - (.+)'
        match2 = re.match(pattern2, line.strip())
        
        if match2:
            timestamp, module, level, message = match2.groups()
            return {
                'timestamp': timestamp,
                'module': module,
                'level': level,
                'message': message,
                'core': extract_log_core(message)
            }
            
        return None
        
    except Exception:
        return None

def analyze_log_file(file_path: Path) -> dict:
    """分析单个日志文件"""
    print(f"📄 分析日志文件: {file_path.name}")
    
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        return {}
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        total_lines = len(lines)
        parsed_logs = []
        
        for line in lines:
            if line.strip():
                log_entry = parse_log_line(line)
                if log_entry:
                    parsed_logs.append(log_entry)
        
        # 统计重复日志
        core_counter = Counter()
        level_counter = Counter()
        duplicate_groups = defaultdict(list)
        
        for log in parsed_logs:
            core = log.get('core', '')
            level = log.get('level', 'UNKNOWN')
            
            if core:
                core_counter[core] += 1
                level_counter[level] += 1
                duplicate_groups[core].append(log)
        
        # 找出重复次数最多的日志
        most_duplicated = core_counter.most_common(10)
        
        # 统计重复日志的总数
        total_duplicates = sum(count - 1 for count in core_counter.values() if count > 1)
        duplicate_ratio = (total_duplicates / total_lines * 100) if total_lines > 0 else 0
        
        return {
            'file_name': file_path.name,
            'total_lines': total_lines,
            'parsed_logs': len(parsed_logs),
            'unique_messages': len(core_counter),
            'total_duplicates': total_duplicates,
            'duplicate_ratio': duplicate_ratio,
            'most_duplicated': most_duplicated,
            'level_distribution': dict(level_counter),
            'duplicate_groups': dict(duplicate_groups)
        }
        
    except Exception as e:
        print(f"❌ 分析文件失败: {e}")
        return {}

def analyze_logs_directory(logs_dir: Path = None) -> dict:
    """分析日志目录中的所有日志文件"""
    if logs_dir is None:
        logs_dir = Path("logs/current")
    
    print(f"🔍 分析日志目录: {logs_dir}")
    
    if not logs_dir.exists():
        print(f"❌ 日志目录不存在: {logs_dir}")
        return {}
    
    log_files = list(logs_dir.glob("*.log"))
    if not log_files:
        print(f"❌ 未找到日志文件在目录: {logs_dir}")
        return {}
    
    print(f"📁 找到 {len(log_files)} 个日志文件")
    
    all_results = {}
    total_stats = {
        'total_files': len(log_files),
        'total_lines': 0,
        'total_duplicates': 0,
        'files_with_duplicates': 0
    }
    
    for log_file in log_files:
        result = analyze_log_file(log_file)
        if result:
            all_results[log_file.name] = result
            total_stats['total_lines'] += result.get('total_lines', 0)
            total_stats['total_duplicates'] += result.get('total_duplicates', 0)
            if result.get('total_duplicates', 0) > 0:
                total_stats['files_with_duplicates'] += 1
    
    total_stats['overall_duplicate_ratio'] = (
        total_stats['total_duplicates'] / total_stats['total_lines'] * 100
        if total_stats['total_lines'] > 0 else 0
    )
    
    return {
        'analysis_time': datetime.now().isoformat(),
        'total_stats': total_stats,
        'file_results': all_results
    }

def print_analysis_report(analysis_result: dict):
    """打印分析报告"""
    print("\n" + "=" * 80)
    print("📊 日志重复分析报告")
    print("=" * 80)
    
    total_stats = analysis_result.get('total_stats', {})
    
    print(f"\n📈 总体统计:")
    print(f"  📁 分析文件数量: {total_stats.get('total_files', 0)}")
    print(f"  📄 总日志行数: {total_stats.get('total_lines', 0):,}")
    print(f"  🔄 重复日志行数: {total_stats.get('total_duplicates', 0):,}")
    print(f"  📊 重复率: {total_stats.get('overall_duplicate_ratio', 0):.2f}%")
    print(f"  🚨 有重复的文件: {total_stats.get('files_with_duplicates', 0)}/{total_stats.get('total_files', 0)}")
    
    file_results = analysis_result.get('file_results', {})
    
    print(f"\n📋 各文件详细分析:")
    for file_name, result in file_results.items():
        print(f"\n  📄 {file_name}:")
        print(f"    📊 总行数: {result.get('total_lines', 0):,}")
        print(f"    🔄 重复行数: {result.get('total_duplicates', 0):,}")
        print(f"    📈 重复率: {result.get('duplicate_ratio', 0):.2f}%")
        
        # 显示最重复的日志
        most_duplicated = result.get('most_duplicated', [])
        if most_duplicated:
            print(f"    🔥 最重复的日志:")
            for i, (core, count) in enumerate(most_duplicated[:3]):
                if count > 1:
                    print(f"      {i+1}. [{count}次] {core[:80]}...")
    
    print(f"\n💡 优化建议:")
    if total_stats.get('overall_duplicate_ratio', 0) > 20:
        print(f"  🚨 重复率过高({total_stats.get('overall_duplicate_ratio', 0):.1f}%)，强烈建议启用日志去重机制")
    elif total_stats.get('overall_duplicate_ratio', 0) > 10:
        print(f"  ⚠️ 重复率较高({total_stats.get('overall_duplicate_ratio', 0):.1f}%)，建议启用日志去重机制")
    else:
        print(f"  ✅ 重复率较低({total_stats.get('overall_duplicate_ratio', 0):.1f}%)，日志质量良好")
    
    print(f"  🔧 建议的去重配置:")
    print(f"    - 抑制时间: 30-60秒")
    print(f"    - 最大缓存: 50条日志")
    print(f"    - 重点关注: '未找到微信窗口'、'自动休息后'等高频错误")

def save_analysis_result(analysis_result: dict, output_file: Path = None):
    """保存分析结果到JSON文件"""
    if output_file is None:
        output_file = Path(f"log_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
        print(f"\n💾 分析结果已保存到: {output_file}")
    except Exception as e:
        print(f"❌ 保存分析结果失败: {e}")

def main():
    """主函数"""
    print("🚀 启动日志重复分析工具")
    
    # 分析当前日志目录
    logs_dir = Path("logs/current")
    analysis_result = analyze_logs_directory(logs_dir)
    
    if analysis_result:
        # 打印分析报告
        print_analysis_report(analysis_result)
        
        # 保存分析结果
        save_analysis_result(analysis_result)
        
        print(f"\n🎉 日志分析完成！")
    else:
        print(f"❌ 日志分析失败，请检查日志目录是否存在")

if __name__ == "__main__":
    main()
