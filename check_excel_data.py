#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel数据内容
验证数据结构和字段内容
"""

import os
import sys
from pathlib import Path

# 添加modules路径
sys.path.append(str(Path(__file__).parent / "modules"))

def check_excel_data():
    """检查Excel数据内容"""
    
    excel_file = "添加好友名单.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return
    
    try:
        from modules.data_manager import DataManager
        
        print(f"📋 正在检查Excel文件: {excel_file}")
        print("=" * 60)
        
        # 创建数据管理器
        data_manager = DataManager(excel_file)
        
        # 加载数据
        contacts_data = data_manager.load_phone_numbers()
        
        if not contacts_data:
            print("⚠️ Excel文件中无数据")
            return
        
        print(f"📊 总记录数: {len(contacts_data)}")
        print("=" * 60)
        
        # 显示前10条记录的详细信息
        for i, contact in enumerate(contacts_data[:10], 1):
            print(f"\n📱 记录 {i}:")
            print(f"  手机号码: {contact.get('phone', 'N/A')}")
            print(f"  状态: {contact.get('status', 'N/A')}")
            print(f"  结果: '{contact.get('result', 'N/A')}'")
            print(f"  更新时间: '{contact.get('update_time', 'N/A')}'")
            print(f"  原始数据: {contact}")
        
        # 统计各字段的数据情况
        print("\n" + "=" * 60)
        print("📈 字段统计:")
        
        # 统计状态
        status_count = {}
        result_count = {}
        time_count = {"有时间": 0, "无时间": 0}
        
        for contact in contacts_data:
            # 状态统计
            status = contact.get('status', '未知')
            status_count[status] = status_count.get(status, 0) + 1
            
            # 结果统计
            result = contact.get('result', '')
            if result and result.strip():
                result_count[result] = result_count.get(result, 0) + 1
            else:
                result_count['空结果'] = result_count.get('空结果', 0) + 1
            
            # 时间统计
            update_time = contact.get('update_time', '')
            if update_time and update_time.strip():
                time_count["有时间"] += 1
            else:
                time_count["无时间"] += 1
        
        print(f"\n🔍 状态分布:")
        for status, count in status_count.items():
            print(f"  {status}: {count}")
        
        print(f"\n🔍 结果分布:")
        for result, count in list(result_count.items())[:5]:  # 显示前5个
            print(f"  {result}: {count}")
        
        print(f"\n🔍 时间分布:")
        for time_type, count in time_count.items():
            print(f"  {time_type}: {count}")
        
        print("\n" + "=" * 60)
        print("✅ Excel数据检查完成")
        
    except Exception as e:
        print(f"❌ 检查Excel数据失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_excel_data()
